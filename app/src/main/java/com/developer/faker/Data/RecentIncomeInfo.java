package com.developer.faker.Data;

import android.support.annotation.Nullable;

/**
 * 最近来电信息数据类
 * 用于存储通话记录的详细信息，实现Comparable接口以支持排序
 */
public class RecentIncomeInfo implements Comparable<RecentIncomeInfo> {
    public String callDate;        // 通话日期
    public int callType;           // 通话类型（电话/短信）
    public int callTypeDetail;     // 通话类型详情（来电/去电/未接等）
    public String color;           // 显示颜色
    public String contactName;     // 联系人姓名
    public String phoneNumber;     // 电话号码

    /**
     * 比较方法，用于排序
     * 按通话日期倒序排列（最新的在前）
     * @param recentIncomeInfo 要比较的对象
     * @return 比较结果
     */
    @Override
    public int compareTo(RecentIncomeInfo recentIncomeInfo) {
        return this.callDate.compareTo(recentIncomeInfo.callDate) * (-1);
    }

    /**
     * 判断两个对象是否相等
     * 基于电话号码进行比较
     * @param obj 要比较的对象
     * @return 是否相等
     */
    public boolean equals(@Nullable Object obj) {
        return ((RecentIncomeInfo) obj).phoneNumber == this.phoneNumber;
    }
}