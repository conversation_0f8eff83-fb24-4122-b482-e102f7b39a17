package com.developer.faker.Data;

import android.support.annotation.Nullable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 电话信息数据类
 * 用于存储和管理电话号码相关的用户信息
 */
public class PhoneInfo {
    /** 唯一标识ID */
    public int id;
    /** 电话号码 */
    public String phoneNumber;
    /** 更新时间戳 */
    public long updatetime;
    /** 用户名称 */
    public String userName;

    /**
     * 获取格式化的更新时间字符串
     * @return 格式化后的时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
     */
    public String getUpdateTimeString() {
        try {
            // 将时间戳转换为指定格式的字符串
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(this.updatetime));
        } catch (Exception e) {
            // 异常处理：打印错误信息并返回默认时间
            e.printStackTrace();
            return "2000-01-01 00:00:00";
        }
    }

    /**
     * 比较两个PhoneInfo对象是否相等
     * @param obj 要比较的对象
     * @return 如果用户名和电话号码都相同则返回true，否则返回false
     */
    public boolean equals(@Nullable Object obj) {
        PhoneInfo phoneInfo = (PhoneInfo) obj;
        // 比较用户名和电话号码是否相同
        return phoneInfo.userName == this.userName && phoneInfo.phoneNumber == this.phoneNumber;
    }
}