package com.developer.faker.Data;

/**
 * 拦截号码历史记录数据类
 * 用于存储和管理拦截号码的历史记录信息
 */
public class BlockNumberHistory {
    /** 类型：全部 */
    public static final int TYPE_ALL = 5;
    /** 类型：通话异常 */
    public static final int TYPE_CALLEXP = 6;
    /** 类型：偏好号码 */
    public static final int TYPE_PREFNUM = 4;
    /** 类型：特殊号码 */
    public static final int TYPE_SPECNUM = 3;
    /** 类型：今日通话 */
    public static final int TYPE_TODAYCALL = 2;
    /** 类型：未知 */
    public static final int TYPE_UNKNOWN = 1;

    /** 日期时间戳 */
    public long dateTick;
    /** 电话号码 */
    public String number;
    /** 记录类型 */
    public int type;
}