package com.developer.faker.Data;

/**
 * 拦截号码数据类
 * 用于存储和管理被拦截的电话号码相关信息
 */
public class BlockNumberData {
    /** 拦截类型：明确拦截 */
    public static int BlockType_Expl = 2;
    /** 拦截类型：偏好拦截 */
    public static int BlockType_Pref = 1;
    /** 拦截类型：特殊拦截 */
    public static int BlockType_Spec;

    /** 拦截类型 */
    public int nBlockType;
    /** 今日拦截次数 */
    public int nTodayCount;
    /** 电话号码 */
    public String phoneNumber;

    /**
     * 构造函数
     * @param str 电话号码
     * @param i 拦截类型
     * @param i2 今日拦截次数
     */
    public BlockNumberData(String str, int i, int i2) {
        this.phoneNumber = str;
        this.nBlockType = i;
        this.nTodayCount = i2;
    }
}