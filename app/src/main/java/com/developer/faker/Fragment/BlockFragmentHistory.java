package com.developer.faker.Fragment;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Adapter.BlockNumberHistoryAdapter;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;

/**
 * 拦截历史记录Fragment
 * 显示和管理电话拦截的历史记录
 */
public class BlockFragmentHistory extends BaseFragment {
    /** 历史记录列表适配器 */
    BlockNumberHistoryAdapter histAdapter;
    /** Fragment根视图 */
    View view = null;
    /** 历史记录列表视图 */
    ListView lstHistory = null;
    /** 总计数显示文本 */
    TextView txtTotalCount = null;
    /** 拦截工具类实例 */
    UtilBlock utilBlock = null;

    /**
     * Fragment创建时的回调
     * 初始化拦截工具类实例
     * @param bundle 保存的实例状态
     */
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局
            this.view = layoutInflater.inflate(R.layout.fragment_block_history, viewGroup, false);
        }
        // 初始化UI
        InitUI();
        return this.view;
    }

    /**
     * 初始化用户界面
     * 设置UI组件并显示拦截历史记录
     */
    public void InitUI() {
        // 初始化UI组件
        this.txtTotalCount = (TextView) this.view.findViewById(R.id.txtTotalCount);
        View viewFindViewById = this.view.findViewById(R.id.layout_nodata);
        this.lstHistory = (ListView) this.view.findViewById(R.id.lstBlockHistory);

        // 设置清除按钮的点击监听器
        this.view.findViewById(R.id.layout_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 显示确认删除对话框
                AlertDialog.Builder builder = new AlertDialog.Builder(view.getContext());
                builder.setTitle("삭제");  // 删除
                builder.setMessage("기록을 전부 삭제하시겠습니까?");  // 是否删除全部记录？
                builder.setPositiveButton("예", new DialogInterface.OnClickListener() {  // 是
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        // 清空拦截历史记录
                        BlockFragmentHistory.this.utilBlock.lstBlockHistory.clear();
                        // 刷新UI显示
                        BlockFragmentHistory.this.InitUI();
                    }
                });
                builder.setNegativeButton("아니", (DialogInterface.OnClickListener) null);  // 否
                builder.show();
            }
        });

        // 获取历史记录数量并更新显示
        int size = this.utilBlock.lstBlockHistory.size();
        this.txtTotalCount.setText(String.valueOf(size) + " 건");  // X 条

        if (size == 0) {
            // 没有历史记录时显示空数据提示
            viewFindViewById.setVisibility(0);  // 显示无数据布局
            this.lstHistory.setVisibility(8);   // 隐藏列表
        } else {
            // 有历史记录时显示列表
            viewFindViewById.setVisibility(8);  // 隐藏无数据布局
            this.lstHistory.setVisibility(0);   // 显示列表
            // 创建并设置适配器
            this.histAdapter = new BlockNumberHistoryAdapter((BaseActivity) getActivity(), 0, this.utilBlock.lstBlockHistory);
            this.lstHistory.setAdapter((ListAdapter) this.histAdapter);
        }
    }
}