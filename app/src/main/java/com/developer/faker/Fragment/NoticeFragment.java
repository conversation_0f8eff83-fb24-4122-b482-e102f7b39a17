package com.developer.faker.Fragment;

import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Adapter.NoticeListAdapter;
import com.developer.faker.Data.NoticeInfo;
import com.developer.faker.R;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.Utils;
import org.json.JSONObject;

/**
 * 通知公告Fragment
 * 显示系统通知和公告信息，支持列表和详情查看
 */
public class NoticeFragment extends BaseFragment {
    /** 通知列表视图 */
    ListView lstReport;
    /** 等待处理的Handler */
    private Handler m_WaitHandler;
    /** 通知列表适配器 */
    private NoticeListAdapter reportListAdapter;
    /** 响应数据 */
    JSONObject responseData;
    /** 详情内容文本 */
    TextView txtDetailContent;
    /** 详情日期文本 */
    TextView txtDetailDate;
    /** 详情标题文本 */
    TextView txtDetailTitle;
    /** 详情视图 */
    View vDetail;
    /** 列表视图 */
    View vList;
    /** Fragment根视图 */
    View view;

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局并初始化UI
            this.view = layoutInflater.inflate(R.layout.fragment_notice, viewGroup, false);
            initUI(this.view);
        }
        return this.view;
    }

    /**
     * 初始化用户界面
     * 设置各种UI组件并配置事件监听器
     * @param view Fragment的根视图
     */
    private void initUI(View view) {
        // 初始化通知列表视图
        this.lstReport = (ListView) view.findViewById(R.id.lstReport);
        // 初始化详情视图和列表视图
        this.vDetail = view.findViewById(R.id.viewDetail);
        this.vList = view.findViewById(R.id.lstReport);
        // 初始化详情页面的文本组件
        this.txtDetailDate = (TextView) view.findViewById(R.id.txtNoticeDate);      // 详情日期
        this.txtDetailTitle = (TextView) view.findViewById(R.id.txtNoticeTitle);    // 详情标题
        this.txtDetailContent = (TextView) view.findViewById(R.id.txtNoticeContent); // 详情内容

        // 设置详情内容支持滚动
        this.txtDetailContent.setMovementMethod(new ScrollingMovementMethod());

        // 默认显示列表视图，隐藏详情视图
        ShowDetail(false);

        // 设置"返回列表"按钮的点击监听器
        view.findViewById(R.id.btnGoList).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                // 点击时返回到列表视图
                NoticeFragment.this.ShowDetail(false);
            }
        });

        // 从服务器获取通知数据
        getSearchResultFromServer(0, 40);
    }

    /**
     * 刷新用户界面
     * 更新通知列表显示并设置列表项点击事件
     */
    public void refreshUI() {
        // 创建通知列表适配器，使用全局通知数据
        this.reportListAdapter = new NoticeListAdapter(this.baseActivity, 0, Global.NoticeList);
        // 设置列表适配器
        this.lstReport.setAdapter((ListAdapter) this.reportListAdapter);

        // 设置列表项点击监听器
        this.lstReport.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> adapterView, View view, int i, long j) {
                // 获取点击的通知信息
                NoticeInfo noticeInfo = Global.NoticeList.get(i);
                // 将通知信息显示到详情页面
                NoticeFragment.this.txtDetailDate.setText(noticeInfo.date);        // 设置日期
                NoticeFragment.this.txtDetailTitle.setText(noticeInfo.subject);    // 设置标题
                NoticeFragment.this.txtDetailContent.setText(noticeInfo.content);  // 设置内容
                // 切换到详情视图
                NoticeFragment.this.ShowDetail(true);
            }
        });
    }

    /**
     * 从服务器获取通知数据
     * 显示加载进度并异步获取通知列表
     * @param i 起始位置参数（暂未使用）
     * @param i2 数量参数（暂未使用）
     */
    private void getSearchResultFromServer(int i, int i2) {
        // 创建处理服务器响应的Handler
        this.m_WaitHandler = new Handler() {
            @Override
            public void handleMessage(Message message) {
                super.handleMessage(message);
                // 数据获取完成后刷新UI
                NoticeFragment.this.refreshUI();
                // 隐藏加载进度对话框
                NoticeFragment.this.dismissProgress();
            }
        };

        // 显示加载进度对话框
        showProgress(getResources().getString(R.string.wait));
        // 调用工具类从服务器获取通知数据
        Utils.getInstance().getNoticeDataFromServer(getContext(), this.m_WaitHandler);
    }

    /**
     * 切换显示模式
     * 在列表视图和详情视图之间切换
     * @param z true显示详情视图，false显示列表视图
     */
    public void ShowDetail(boolean z) {
        // 将详情内容滚动到顶部
        this.txtDetailContent.scrollTo(0, 0);

        if (z) {
            // 显示详情视图，隐藏列表视图
            this.vDetail.setVisibility(0);    // 显示详情视图
            this.vList.setVisibility(4);      // 隐藏列表视图（INVISIBLE）
        } else {
            // 显示列表视图，隐藏详情视图
            this.vDetail.setVisibility(4);    // 隐藏详情视图（INVISIBLE）
            this.vList.setVisibility(0);      // 显示列表视图
        }
    }
}