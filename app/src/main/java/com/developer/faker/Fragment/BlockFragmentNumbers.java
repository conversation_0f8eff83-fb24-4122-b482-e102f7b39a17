package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Adapter.BlockNumberListAdapter;
import com.developer.faker.Data.BlockNumberData;
import com.developer.faker.Data.BlockNumberHistory;
import com.developer.faker.Model.BlockNumberDeleteListener;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;
import java.util.ArrayList;
import java.util.Iterator;

/**
 * 拦截号码管理Fragment
 * 显示和管理用户设置的拦截号码列表
 */
public class BlockFragmentNumbers extends BaseFragment {
    /** Fragment根视图 */
    View view = null;
    /** 总计数显示文本 */
    TextView txtTotalCount = null;
    /** 号码列表视图 */
    ListView lstNumbers = null;
    /** 拦截号码数据列表 */
    ArrayList<BlockNumberData> lstBlockNumberData = new ArrayList<>();
    /** 拦截号码列表适配器 */
    BlockNumberListAdapter blockAdater = null;
    /** 拦截工具类实例 */
    UtilBlock utilBlock = null;

    /**
     * Fragment创建时的回调
     * 初始化拦截工具类实例
     * @param bundle 保存的实例状态
     */
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局
            this.view = layoutInflater.inflate(R.layout.fragment_block_numbers, viewGroup, false);
        }
        // 初始化UI
        InitUI();
        return this.view;
    }

    /**
     * 初始化用户界面
     * 加载并显示拦截号码列表，统计拦截次数
     */
    public void InitUI() {
        // 初始化UI组件
        this.txtTotalCount = (TextView) this.view.findViewById(R.id.txtTotalCount);
        View viewFindViewById = this.view.findViewById(R.id.layout_nodata);
        this.lstNumbers = (ListView) this.view.findViewById(R.id.lstBlockHistory);

        // 计算总的拦截号码数量（偏好号码 + 特殊号码）
        int size = this.utilBlock.lstPrefNumbers.size() + this.utilBlock.lstSpecNumbers.size();
        this.txtTotalCount.setText(String.valueOf(size) + " 건");  // X 条

        if (size == 0) {
            // 没有拦截号码时显示空数据提示
            viewFindViewById.setVisibility(0);  // 显示无数据布局
            this.lstNumbers.setVisibility(8);   // 隐藏列表
            return;
        }

        // 有拦截号码时显示列表
        viewFindViewById.setVisibility(8);  // 隐藏无数据布局
        this.lstNumbers.setVisibility(0);   // 显示列表
        this.lstBlockNumberData.clear();    // 清空数据列表

        // 处理偏好拦截号码列表
        Iterator<String> it = this.utilBlock.lstPrefNumbers.iterator();
        while (it.hasNext()) {
            String next = it.next();
            // 统计该偏好号码的拦截次数
            Iterator<BlockNumberHistory> it2 = this.utilBlock.lstBlockHistory.iterator();
            int i = 0;
            while (it2.hasNext()) {
                BlockNumberHistory next2 = it2.next();
                // 检查是否是偏好号码类型且号码匹配（前缀匹配）
                if (next2.type == 4 && next2.number.startsWith(next)) {
                    i++;
                }
            }
            // 添加到显示列表（类型为1表示偏好拦截）
            this.lstBlockNumberData.add(new BlockNumberData(next, 1, i));
        }

        // 处理特殊拦截号码列表
        Iterator<String> it3 = this.utilBlock.lstSpecNumbers.iterator();
        while (it3.hasNext()) {
            String next3 = it3.next();
            // 统计该特殊号码的拦截次数
            Iterator<BlockNumberHistory> it4 = this.utilBlock.lstBlockHistory.iterator();
            int i2 = 0;
            while (it4.hasNext()) {
                BlockNumberHistory next4 = it4.next();
                // 检查是否是特殊号码类型且号码完全匹配
                if (next4.type == 3 && next4.number.equals(next3)) {
                    i2++;
                }
            }
            // 添加到显示列表（类型为0表示特殊拦截）
            this.lstBlockNumberData.add(new BlockNumberData(next3, 0, i2));
        }

        // 创建并设置适配器
        this.blockAdater = new BlockNumberListAdapter((BaseActivity) getActivity(), 0, this.lstBlockNumberData);

        // 设置删除按钮的点击监听器
        this.blockAdater.setOnClickCloseListener(new BlockNumberDeleteListener() {
            @Override
            public void onResult(BlockNumberData blockNumberData) {
                // 根据拦截类型从相应列表中删除号码
                if (blockNumberData.nBlockType == BlockNumberData.BlockType_Pref) {
                    // 删除偏好拦截号码
                    BlockFragmentNumbers.this.utilBlock.lstPrefNumbers.remove(blockNumberData.phoneNumber);
                } else if (blockNumberData.nBlockType == BlockNumberData.BlockType_Spec) {
                    // 删除特殊拦截号码
                    BlockFragmentNumbers.this.utilBlock.lstSpecNumbers.remove(blockNumberData.phoneNumber);
                } else {
                    // 删除通话异常号码
                    BlockFragmentNumbers.this.utilBlock.lstCallExplosion.remove(blockNumberData.phoneNumber);
                }
                // 刷新UI显示
                BlockFragmentNumbers.this.InitUI();
            }
        });

        // 设置列表适配器
        this.lstNumbers.setAdapter((ListAdapter) this.blockAdater);
    }
}