package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import com.developer.faker.R;

/**
 * 网页搜索Fragment
 * 使用WebView显示网页内容，支持JavaScript和缩放功能
 */
public class WebSearchFragment extends BaseFragment {
    /** URL参数键名 */
    private static final String ARG_URL = "web_url";
    /** 要加载的URL地址 */
    private String strUrl;
    /** Fragment根视图 */
    View view;
    /** WebView组件 */
    WebView webView;

    /**
     * 创建WebSearchFragment实例的静态方法
     * @param str 要加载的URL地址
     * @return WebSearchFragment实例
     */
    public static WebSearchFragment NewInstance(String str) {
        WebSearchFragment webSearchFragment = new WebSearchFragment();
        Bundle bundle = new Bundle();
        bundle.putString(ARG_URL, str);
        webSearchFragment.setArguments(bundle);
        return webSearchFragment;
    }

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        // 获取传入的URL参数
        if (getArguments() != null) {
            this.strUrl = getArguments().getString(ARG_URL);
        }
        if (this.view == null) {
            // 填充布局并初始化UI
            this.view = layoutInflater.inflate(R.layout.fragment_websearch, viewGroup, false);
            initUI(this.view);
        }
        return this.view;
    }

    /**
     * 初始化用户界面
     * 配置WebView的各种设置并加载URL
     * @param view Fragment的根视图
     */
    private void initUI(View view) {
        // 初始化WebView
        this.webView = (WebView) view.findViewById(R.id.webView);
        // 启用JavaScript支持
        this.webView.getSettings().setJavaScriptEnabled(true);
        // 启用DOM存储
        this.webView.getSettings().setDomStorageEnabled(true);
        // 加载指定URL
        this.webView.loadUrl(this.strUrl);
        // 设置WebView客户端
        this.webView.setWebViewClient(new WebViewClient());
        // 设置初始缩放比例
        this.webView.setInitialScale(1);
        // 启用内置缩放控件
        this.webView.getSettings().setBuiltInZoomControls(true);
        // 启用宽视口支持
        this.webView.getSettings().setUseWideViewPort(true);
    }
}