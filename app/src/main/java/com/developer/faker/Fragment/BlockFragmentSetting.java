package com.developer.faker.Fragment;

import android.app.Dialog;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilBlock;
import com.developer.faker.Utils.Utils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.JsonNode;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import java.util.HashMap;
import java.util.Iterator;

/**
 * 拦截设置Fragment
 * 管理各种拦截功能的开关设置和号码添加
 */
public class BlockFragmentSetting extends BaseFragment {
    /** Fragment根视图 */
    View view = null;
    /** 拦截未知号码开关 */
    Switch swhBlockUnknown = null;
    /** 拦截今日通话开关 */
    Switch swhBlockTodayCall = null;
    /** 拦截特殊号码开关 */
    Switch swhBlockSpecNumber = null;
    /** 拦截偏好号码开关 */
    Switch swhBlockPref = null;
    /** 拦截全部开关 */
    Switch swhBlockAll = null;
    /** 拦截通话异常开关 */
    Switch swhBlockCallExplosion = null;
    /** 拦截工具类实例 */
    private UtilBlock utilBlock = null;

    /**
     * Fragment创建时的回调
     * 初始化拦截工具类实例
     * @param bundle 保存的实例状态
     */
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.utilBlock = UtilBlock.getInstance(getContext());
    }

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View view = this.view;
        if (view != null) {
            return view;
        }
        // 填充布局并初始化UI
        this.view = layoutInflater.inflate(R.layout.fragment_block_setting, viewGroup, false);
        InitUI();
        return this.view;
    }

    /**
     * 初始化用户界面
     * 设置各种拦截开关和按钮的监听器，并根据当前设置初始化开关状态
     */
    private void InitUI() {
        // 初始化拦截未知号码开关
        this.swhBlockUnknown = (Switch) this.view.findViewById(R.id.swhBlockUnknownNumber);
        this.swhBlockUnknown.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockUnknown(view);
            }
        });

        // 初始化拦截今日通话开关
        this.swhBlockTodayCall = (Switch) this.view.findViewById(R.id.swhBlockTodayCall);
        this.swhBlockTodayCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockTodayCall(view);
            }
        });

        // 初始化拦截特殊号码开关
        this.swhBlockSpecNumber = (Switch) this.view.findViewById(R.id.swhBlockSpecNumber);
        this.swhBlockSpecNumber.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockSpecNumber(view);
            }
        });

        // 初始化拦截前缀号码开关
        this.swhBlockPref = (Switch) this.view.findViewById(R.id.swhBlockPrefixNumber);
        this.swhBlockPref.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockPrefNumber(view);
            }
        });

        // 初始化拦截全部号码开关
        this.swhBlockAll = (Switch) this.view.findViewById(R.id.swhBlockAllNumber);
        this.swhBlockAll.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhBlockAllNumber(view);
            }
        });

        // 初始化拦截通话异常开关
        this.swhBlockCallExplosion = (Switch) this.view.findViewById(R.id.swhCallExplosion);
        this.swhBlockCallExplosion.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                BlockFragmentSetting.this.onClickSwhCallExplosion(view);
            }
        });

        // 设置各种添加按钮的点击监听器
        // 设置"今日通话拦截限制"按钮的点击监听器
        this.view.findViewById(R.id.btnBlockTodayCall).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击时调用设置今日通话拦截限制的方法，显示数量设置对话框
                BlockFragmentSetting.this.onBtnClickBlockTodayCall(view);
            }
        });

        // 设置"添加特殊拦截号码"按钮的点击监听器
        this.view.findViewById(R.id.btnBlockSpecNumber).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击时调用添加特殊号码的方法，显示输入对话框
                BlockFragmentSetting.this.onBtnClickBlockSpecNumber(view);
            }
        });

        // 设置"添加前缀拦截号码"按钮的点击监听器
        this.view.findViewById(R.id.btnBlockPrefNumber).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击时调用添加前缀号码的方法，显示输入对话框
                BlockFragmentSetting.this.onBtnClickBlockPrefNumber(view);
            }
        });

        // 设置"通话异常设置"按钮的点击监听器
        this.view.findViewById(R.id.btnCallExplosion).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 点击时调用通话异常设置的方法，显示配置对话框
                BlockFragmentSetting.this.onBtnClickBlockCallExplosion(view);
            }
        });

        // 根据当前设置初始化各开关的状态
        this.swhBlockUnknown.setChecked(this.utilBlock.IsBlockUnknown);
        this.swhBlockSpecNumber.setChecked(this.utilBlock.IsBlockSpecNumbers);
        this.swhBlockTodayCall.setChecked(this.utilBlock.IsBlockTodayCall);
        this.swhBlockPref.setChecked(this.utilBlock.IsBlockPrefNumbers);
        this.swhBlockAll.setChecked(this.utilBlock.IsBlockAll);
        this.swhBlockCallExplosion.setChecked(this.utilBlock.IsBlockCallExp);
    }

    /**
     * 添加特殊拦截号码按钮点击事件
     * 显示对话框让用户输入要拦截的特殊号码
     * @param view 点击的视图
     */
    public void onBtnClickBlockSpecNumber(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockspecnum);

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                boolean z;
                if (view2.getId() == R.id.btnOk) {
                    // 获取输入的电话号码
                    EditText editText = (EditText) dialog.findViewById(R.id.txtPhoneNumber);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                    String correctPhoneNumber = Utils.getCorrectPhoneNumber(editText.getText().toString());

                    // 验证电话号码长度
                    if (correctPhoneNumber.length() >= 8) {
                        // 检查号码是否已存在
                        Iterator<String> it = BlockFragmentSetting.this.utilBlock.lstSpecNumbers.iterator();
                        while (true) {
                            if (!it.hasNext()) {
                                z = false;  // 号码不存在
                                break;
                            } else if (it.next().compareTo(correctPhoneNumber) == 0) {
                                z = true;   // 号码已存在
                                break;
                            }
                        }

                        if (!z) {
                            // 添加新的特殊拦截号码
                            BlockFragmentSetting.this.utilBlock.lstSpecNumbers.add(correctPhoneNumber);
                        } else {
                            // 显示号码已存在的错误信息
                            textView.setText("입력하신 전화번호가 이미 존재 합니다");  // 输入的电话号码已存在
                            textView.setVisibility(0);
                            return;
                        }
                    } else {
                        // 显示号码格式错误的信息
                        textView.setText("입력하신 전화번호가 옳바르지 않습니다");  // 输入的电话号码不正确
                        textView.setVisibility(0);
                        return;
                    }
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };

        // 设置各按钮的点击监听器
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);

        // 设置对话框宽度并显示
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 添加前缀拦截号码按钮点击事件
     * 显示对话框让用户输入要拦截的前缀号码
     * @param view 点击的视图
     */
    public void onBtnClickBlockPrefNumber(View view) {
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockprefnum);

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                boolean z;
                if (view2.getId() == R.id.btnOk) {
                    // 获取输入的前缀号码
                    EditText editText = (EditText) dialog.findViewById(R.id.txtPhoneNumber);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                    String correctPhoneNumber = Utils.getCorrectPhoneNumber(editText.getText().toString());

                    // 验证前缀号码长度（至少4位）
                    if (correctPhoneNumber.length() >= 4) {
                        // 检查前缀是否已存在
                        Iterator<String> it = BlockFragmentSetting.this.utilBlock.lstPrefNumbers.iterator();
                        while (true) {
                            z = true;
                            if (!it.hasNext()) {
                                z = false;  // 前缀不存在
                                break;
                            } else if (correctPhoneNumber.startsWith(it.next())) {
                                break;      // 前缀已存在
                            }
                        }

                        if (!z) {
                            // 添加新的前缀拦截号码
                            BlockFragmentSetting.this.utilBlock.lstPrefNumbers.add(correctPhoneNumber);
                        } else {
                            // 显示前缀已存在的错误信息
                            textView.setText("입력하신 전화번호가 이미 존재 합니다");  // 输入的电话号码已存在
                            textView.setVisibility(0);
                            return;
                        }
                    } else {
                        // 显示前缀格式错误的信息
                        textView.setText("입력하신 전화번호가 옳바르지 않습니다");  // 输入的电话号码不正确
                        textView.setVisibility(0);
                        return;
                    }
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 设置今日通话拦截限制按钮点击事件
     * 显示对话框让用户设置今日通话拦截的次数限制
     * @param view 点击的视图
     */
    public void onBtnClickBlockTodayCall(View view) {
        // 创建设置今日通话拦截限制的对话框
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blocktodaycall);

        // 获取输入框并设置当前的拦截限制数值
        final EditText editText = (EditText) dialog.findViewById(R.id.txtBlockLimit);
        editText.setText(Integer.toString(this.utilBlock.nBlockLimitTodayCall));

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view){
                if (view.getId() == R.id.btnOk) {
                    try {
                        // 获取用户输入的数值并进行验证
                        int i = Integer.parseInt(editText.getText().toString());
                        if (i > 0) {
                            // 输入有效，更新拦截限制数值
                            BlockFragmentSetting.this.utilBlock.nBlockLimitTodayCall = i;
                        } else {
                            // 输入的数值无效（小于等于0）
                            throw new Exception("Invalid Input");
                        }
                    } catch (Exception unused) {
                        // 输入格式错误或数值无效时显示错误信息
                        TextView textView = (TextView) dialog.findViewById(R.id.txtError);
                        textView.setText("입력하신 숫자가 옳바르지 않습니다");  // 输入的数字不正确
                        textView.setVisibility(0);  // 显示错误提示
                        return;
                    }
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };

        // 为对话框的各个按钮设置点击监听器
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);      // 确定按钮
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);   // 关闭按钮
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);  // 取消按钮

        // 设置对话框宽度为全屏并显示
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 通话异常设置按钮点击事件
     * 显示对话框让用户设置通话异常检测的次数阈值
     * @param view 点击的视图
     */
    public void onBtnClickBlockCallExplosion(View view) {
        // 创建通话异常设置对话框
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockcallexp);

        // 设置当前的通话异常检测次数
        ((EditText) dialog.findViewById(R.id.txtCount)).setText(String.valueOf(this.utilBlock.callExplosionCount));

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view){
                int i;
                if (view.getId() == R.id.btnOk) {
                    // 获取用户输入的次数值
                    EditText editText = (EditText) dialog.findViewById(R.id.txtCount);
                    TextView textView = (TextView) dialog.findViewById(R.id.txtError);

                    try {
                        // 解析用户输入的数值
                        i = Integer.parseInt(Utils.getCorrectPhoneNumber(editText.getText().toString()));
                    } catch (Exception unused) {
                        // 解析失败时设置默认值
                        i = 1;
                    }

                    if (i > 1) {
                        // 输入有效（大于1），更新通话异常检测次数
                        BlockFragmentSetting.this.utilBlock.callExplosionCount = i;
                        // 清空现有的通话异常号码列表
                        BlockFragmentSetting.this.utilBlock.lstCallExplosion.clear();
                    } else {
                        // 输入无效时显示错误信息
                        textView.setText("입력하신 갯수가 옳바르지 않습니다");  // 输入的数量不正确
                        textView.setVisibility(0);  // 显示错误提示
                        return;
                    }
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };

        // 为对话框的各个按钮设置点击监听器
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);      // 确定按钮
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);   // 关闭按钮
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);  // 取消按钮

        // 设置对话框宽度为全屏并显示
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 向服务器发送拦截全部配置
     * 将拦截全部号码的设置状态同步到服务器
     */
    public void sendBlockAllConfigToServer() {
        try {
            // 构建服务器API地址
            final String str = Utils.getServerUrl() + Const.API_BLOCK_CONFIG;

            // 准备请求头参数
            final HashMap map = new HashMap();
            map.put("apptype", "3");  // 应用类型
            map.put("token", UtilAuth.getInstance(getContext()).UserToken);  // 用户令牌

            // 在新线程中发送网络请求
            new Thread(new Runnable() {
                @Override
                public void run() {
                    // 发送POST请求，将拦截全部的状态发送到服务器
                    Unirest.post(str)
                        .headers(map)  // 设置请求头
                        .field("flag", Boolean.valueOf(BlockFragmentSetting.this.utilBlock.IsBlockAll))  // 发送拦截状态
                        .asJsonAsync(new Callback<JsonNode>() {
                            @Override
                            public void cancelled() {
                                // 请求被取消时的处理（暂无操作）
                            }

                            @Override
                            public void completed(HttpResponse<JsonNode> httpResponse) {
                                // 请求完成时的处理（暂无操作）
                            }

                            @Override
                            public void failed(UnirestException unirestException) {
                                // 请求失败时的处理（暂无操作）
                            }
                        });
                }
            }).start();
        } catch (Exception e) {
            // 异常处理：打印错误信息
            e.printStackTrace();
        }
    }

    /**
     * 拦截全部号码开关点击事件
     * 处理拦截全部号码功能的开启/关闭，需要用户确认
     * @param view 点击的视图
     */
    public void onClickSwhBlockAllNumber(View view) {
        if (this.utilBlock.IsBlockAll) {
            // 如果当前已开启拦截全部，则直接关闭
            this.utilBlock.IsBlockAll = false;
            // 向服务器发送配置更新
            sendBlockAllConfigToServer();
            return;
        }

        // 如果当前未开启，先将开关设为关闭状态，显示确认对话框
        this.swhBlockAll.setChecked(false);
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_block_all);

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                switch (view2.getId()) {
                    case R.id.btnCancel:  // 取消按钮
                    case R.id.btnClose:   // 关闭按钮
                        // 用户取消，保持拦截全部功能关闭
                        BlockFragmentSetting.this.utilBlock.IsBlockAll = false;
                        break;
                    case R.id.btnOk:     // 确定按钮
                        // 用户确认，开启拦截全部功能
                        BlockFragmentSetting.this.swhBlockAll.setChecked(true);
                        BlockFragmentSetting.this.utilBlock.IsBlockAll = true;
                        // 向服务器发送配置更新
                        BlockFragmentSetting.this.sendBlockAllConfigToServer();
                        break;
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };

        // 为对话框的各个按钮设置点击监听器
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);      // 确定按钮
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);   // 关闭按钮
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);  // 取消按钮

        // 设置对话框宽度为全屏并显示
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 拦截未知号码开关点击事件
     * 处理拦截未知号码功能的开启/关闭，需要用户确认
     * @param view 点击的视图
     */
    public void onClickSwhBlockUnknown(View view) {
        if (this.utilBlock.IsBlockUnknown) {
            // 如果当前已开启拦截未知号码，则直接关闭
            this.utilBlock.IsBlockUnknown = false;
            return;
        }

        // 如果当前未开启，先将开关设为关闭状态，显示确认对话框
        this.swhBlockUnknown.setChecked(false);
        final Dialog dialog = new Dialog(view.getContext());
        dialog.setContentView(R.layout.popup_blockunknown);

        // 设置对话框按钮的点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                switch (view2.getId()) {
                    case R.id.btnCancel:  // 取消按钮
                    case R.id.btnClose:   // 关闭按钮
                        // 用户取消，保持拦截未知号码功能关闭
                        BlockFragmentSetting.this.utilBlock.IsBlockUnknown = false;
                        break;
                    case R.id.btnOk:     // 确定按钮
                        // 用户确认，开启拦截未知号码功能
                        BlockFragmentSetting.this.swhBlockUnknown.setChecked(true);
                        BlockFragmentSetting.this.utilBlock.IsBlockUnknown = true;
                        break;
                }
                // 关闭对话框
                dialog.dismiss();
            }
        };

        // 为对话框的各个按钮设置点击监听器
        dialog.findViewById(R.id.btnOk).setOnClickListener(onClickListener);      // 确定按钮
        dialog.findViewById(R.id.btnClose).setOnClickListener(onClickListener);   // 关闭按钮
        dialog.findViewById(R.id.btnCancel).setOnClickListener(onClickListener);  // 取消按钮

        // 设置对话框宽度为全屏并显示
        setDlgWidthFull(dialog);
        dialog.show();
    }

    /**
     * 设置对话框宽度为全屏
     * 获取屏幕宽度并设置对话框宽度为屏幕宽度
     * @param dialog 要设置的对话框
     */
    private void setDlgWidthFull(Dialog dialog) {
        // 获取屏幕尺寸信息
        DisplayMetrics displayMetrics = getContext().getResources().getDisplayMetrics();
        int i = displayMetrics.widthPixels;   // 屏幕宽度
        int i2 = displayMetrics.heightPixels; // 屏幕高度
        // 设置对话框宽度为屏幕宽度
        dialog.getWindow().getAttributes().width = i;
    }

    /**
     * 拦截今日通话开关点击事件
     * 更新拦截今日通话的设置状态
     * @param view 点击的视图
     */
    public void onClickSwhBlockTodayCall(View view) {
        this.utilBlock.IsBlockTodayCall = this.swhBlockTodayCall.isChecked();
    }

    /**
     * 拦截特殊号码开关点击事件
     * 更新拦截特殊号码的设置状态
     * @param view 点击的视图
     */
    public void onClickSwhBlockSpecNumber(View view) {
        this.utilBlock.IsBlockSpecNumbers = this.swhBlockSpecNumber.isChecked();
    }

    /**
     * 拦截前缀号码开关点击事件
     * 更新拦截前缀号码的设置状态
     * @param view 点击的视图
     */
    public void onClickSwhBlockPrefNumber(View view) {
        this.utilBlock.IsBlockPrefNumbers = this.swhBlockPref.isChecked();
    }

    /**
     * 拦截通话异常开关点击事件
     * 更新拦截通话异常的设置状态并清空异常号码列表
     * @param view 点击的视图
     */
    public void onClickSwhCallExplosion(View view) {
        this.utilBlock.IsBlockCallExp = this.swhBlockCallExplosion.isChecked();
        // 清空通话异常号码列表
        this.utilBlock.lstCallExplosion.clear();
    }
}