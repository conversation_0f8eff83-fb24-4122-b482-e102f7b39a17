package com.developer.faker.Fragment;

import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.support.v4.app.Fragment;
import android.widget.Toast;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Utils.Utils;

/**
 * Fragment基础类
 * 提供所有Fragment的通用功能，包括对话框显示、进度条管理等
 */
public class BaseFragment extends Fragment {
    /** 基础Activity引用 */
    public BaseActivity baseActivity;
    /** 进度对话框 */
    private ProgressDialog mProgressDlg;

    /**
     * Fragment创建时的回调方法
     * 初始化基础Activity引用
     * @param bundle 保存的实例状态
     */
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        // 获取并保存Activity引用
        this.baseActivity = (BaseActivity) getActivity();
    }

    /**
     * Fragment恢复时的回调方法
     * 隐藏软键盘并刷新选项菜单
     */
    @Override
    public void onResume() {
        super.onResume();
        try {
            BaseActivity baseActivity = (BaseActivity) getActivity();
            // 隐藏软键盘
            baseActivity.hideSoftKeyboard();
            // 刷新选项菜单
            baseActivity.invalidateOptionsMenu();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 显示警告对话框
     * @param str 对话框标题
     * @param str2 对话框消息内容
     */
    protected void showAlertDialog(String str, String str2) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(str);
        builder.setMessage(str2);
        builder.setPositiveButton("OK", (DialogInterface.OnClickListener) null);
        builder.show();
    }

    /**
     * 显示Toast消息
     * @param str 要显示的消息内容
     */
    protected void showToast(String str) {
        Toast.makeText(getContext(), str, 0).show();
    }

    /**
     * 显示进度对话框
     * @param str 进度对话框显示的消息
     */
    protected void showProgress(String str) {
        this.mProgressDlg = Utils.getInstance().openNewDialog(getContext(), str, false, false);
    }

    /**
     * 关闭进度对话框
     * 安全地关闭进度对话框，包含异常处理
     */
    protected void dismissProgress() {
        try {
            // 检查对话框是否存在且正在显示
            if (this.mProgressDlg == null || !this.mProgressDlg.isShowing()) {
                return;
            }
            // 关闭对话框
            this.mProgressDlg.dismiss();
        } catch (Exception unused) {
            // 忽略关闭时的异常
        }
    }
}