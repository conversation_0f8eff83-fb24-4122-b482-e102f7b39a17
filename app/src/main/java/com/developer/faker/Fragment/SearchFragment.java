package com.developer.faker.Fragment;

import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;
import com.developer.faker.Activity.LoginActivity;
import com.developer.faker.Adapter.SearchResultListAdapter;
import com.developer.faker.Data.SearchResultData;
import com.developer.faker.Model.MyException;
import com.developer.faker.R;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.RC4;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilContact;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import android.widget.ListAdapter;

/**
 * 搜索结果Fragment
 * 显示电话号码搜索结果，包括号码信息、公司信息等
 */
public class SearchFragment extends BaseFragment implements View.OnClickListener {
    /**
     * 电话号码查询结果处理器
     * 处理从服务器返回的查询结果或认证失败的情况
     */
    private Handler QueryPhoneNumberResultHandler = new Handler() {
        @Override
        public void handleMessage(Message message) {
            super.handleMessage(message);
            // 隐藏加载进度对话框
            SearchFragment.this.dismissProgress();

            if (message.what == 0) {
                // 查询成功，处理返回的数据
                try {
                    // 解析JSON数据并刷新UI
                    SearchFragment.this.refreshUI(new JSONObject(message.obj.toString()));
                    return;
                } catch (Exception e) {
                    // JSON解析失败，打印错误信息
                    e.printStackTrace();
                    return;
                }
            }

            // 查询失败，可能是认证过期，清除用户令牌并跳转到登录页面
            UtilAuth utilAuth = UtilAuth.getInstance(SearchFragment.this.getContext());
            utilAuth.UserToken = null;  // 清除用户令牌
            utilAuth.saveAuthInfo();    // 保存认证信息

            // 创建登录页面的Intent
            Intent intent = new Intent(SearchFragment.this.baseActivity.getBaseContext(), (Class<?>) LoginActivity.class);
            intent.addFlags(67108864);  // FLAG_ACTIVITY_NEW_TASK
            intent.addFlags(1048576);   // FLAG_ACTIVITY_CLEAR_TOP

            // 启动登录页面并添加过渡动画
            SearchFragment.this.startActivity(intent);
            SearchFragment.this.baseActivity.overridePendingTransition(R.anim.slidein, R.anim.slideout);
            SearchFragment.this.baseActivity.finish();  // 结束当前Activity
        }
    };
    View facebookBT;
    View googleBT;
    View lineBT;
    FrameLayout m_layoutWatermark;
    View makeCallBT;
    TextView searchResultCount;
    TextView searchResultEmptyTV;
    ListView searchResultLV;
    SearchResultListAdapter searchResultListAdapter;
    TextView searchResultTV;
    View sendSMSBT;
    View talkBT;
    View view;

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充搜索结果布局
            this.view = layoutInflater.inflate(R.layout.fragment_search, viewGroup, false);
            // 初始化UI组件
            initUI(this.view);
        }
        return this.view;
    }

    /**
     * 初始化用户界面
     * 设置搜索结果显示组件和各种操作按钮
     * @param view Fragment的根视图
     */
    private void initUI(View view) {
        String str;

        // 初始化搜索结果相关的UI组件
        this.searchResultTV = (TextView) view.findViewById(R.id.searchresultTX);        // 搜索结果标题
        this.searchResultCount = (TextView) view.findViewById(R.id.searchResultCount);  // 结果数量显示
        this.searchResultLV = (ListView) view.findViewById(R.id.searchResultLV);        // 搜索结果列表
        this.searchResultEmptyTV = (TextView) view.findViewById(R.id.searchResultEmptyTV); // 空结果提示

        // 初始状态：隐藏结果列表，显示空结果提示
        this.searchResultLV.setVisibility(8);      // 隐藏列表
        this.searchResultEmptyTV.setVisibility(0); // 显示空结果提示

        // 初始化水印布局
        this.m_layoutWatermark = (FrameLayout) view.findViewById(R.id.layoutWatermark);

        // 格式化显示的电话号码（添加连字符）
        String strReplaceAll = Global.Incoming_Call_Number.replaceAll("-", "");
        try {
            // 尝试按标准格式添加连字符：XXX-XXXX-XXXX
            str = strReplaceAll.substring(0, 3) + "-" + strReplaceAll.substring(3, 7) + "-" + strReplaceAll.substring(7);
        } catch (Exception unused) {
            // 格式化失败时使用原始号码
            str = strReplaceAll;
        }
        // 设置显示的电话号码
        this.searchResultTV.setText("전화번호 : " + str);  // 电话号码：XXX-XXXX-XXXX

        // 初始化各种操作按钮
        this.makeCallBT = view.findViewById(R.id.makeCallBT);    // 拨打电话按钮
        this.sendSMSBT = view.findViewById(R.id.sendSMSBT);      // 发送短信按钮
        this.googleBT = view.findViewById(R.id.googleBT);        // Google搜索按钮
        this.facebookBT = view.findViewById(R.id.facebookBT);    // Facebook搜索按钮
        this.talkBT = view.findViewById(R.id.talkBT);            // Talk搜索按钮
        this.lineBT = view.findViewById(R.id.lineBT);            // Line搜索按钮
        // 为所有操作按钮设置点击监听器
        this.makeCallBT.setOnClickListener(this);   // 拨打电话
        this.sendSMSBT.setOnClickListener(this);    // 发送短信
        this.googleBT.setOnClickListener(this);     // Google搜索
        this.facebookBT.setOnClickListener(this);   // Facebook搜索
        this.talkBT.setOnClickListener(this);       // Talk搜索
        this.lineBT.setOnClickListener(this);       // Line搜索

        // 重新获取水印布局（可能是重复代码）
        this.m_layoutWatermark = (FrameLayout) view.findViewById(R.id.layoutWatermark);
        // 设置带有公司水印的背景图片
        this.m_layoutWatermark.setBackground(new BitmapDrawable(getContext().getResources(),
            Utils.getInstance().MakeWaterMark(
                BitmapFactory.decodeResource(getResources(), R.drawable.search_back),
                UtilAuth.getInstance(getContext()).UserCompany,
                20)));

        // 从服务器获取搜索结果
        getSearchResultFromServer(strReplaceAll);
    }

    /**
     * 刷新用户界面
     * 解析服务器返回的搜索结果数据并更新UI显示
     * @param jSONObject 服务器返回的JSON数据
     * @throws MyException 自定义异常
     * @throws JSONException JSON解析异常
     * @throws ParseException 日期解析异常
     */
    public void refreshUI(JSONObject jSONObject) throws MyException, JSONException, ParseException {
        // 创建搜索结果数据列表
        ArrayList<SearchResultData> arrayList = new ArrayList<>();
        // 设置默认的空数据提示
        this.searchResultEmptyTV.setText(R.string.empty_data);

        JSONObject jSONObject2;
        try {
            // 解密服务器返回的数据
            jSONObject2 = new JSONObject(new String(RC4.getInstance().decrypt(
                jSONObject.get("data").toString(),
                Const.AUTH_KEY).getBytes("UTF-8")));
        } catch (Exception e) {
            // 解密失败，显示服务失败信息
            this.searchResultEmptyTV.setText(R.string.service_fail);
            this.searchResultCount.setText("총 0건");  // 总共0条
            this.searchResultLV.setVisibility(8);      // 隐藏结果列表
            this.searchResultEmptyTV.setVisibility(0); // 显示错误提示
            return;
        }

        // 检查是否有错误信息
        if (jSONObject2.has("Error")) {
            this.searchResultEmptyTV.setText(jSONObject2.getString("Error"));
            throw new MyException("Error");
        }

        // 解析搜索结果数据数组
        JSONArray jSONArray = new JSONArray(jSONObject2.getString("Data"));
        // 更新全局剩余查询次数
        Global.RemainQueryCount = jSONObject2.getInt("RemainQueryCount");
        // 更新用户剩余时间
        UtilAuth.getInstance(getContext()).SetRemainMinutes(jSONObject2.getInt("RemainMinutes"));
        // 获取结果数量
        int length = jSONArray.length();

        // 遍历处理每个搜索结果项
        for (int i = 0; i < jSONArray.length(); i++) {
            try {
                // 创建搜索结果数据对象
                SearchResultData searchResultData = new SearchResultData();

                // 处理更新日期
                String string = jSONArray.getJSONObject(i).getString("UpdatedDate");
                Calendar calendar = Calendar.getInstance();
                Date date = null;
                try {
                    // 解析ISO格式的日期字符串
                    date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(string);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (date != null) {
                    // 格式化日期为显示格式
                    calendar.setTime(date);
                    searchResultData.date = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss").format(calendar.getTime());
                } else {
                    // 日期解析失败时设为空字符串
                    searchResultData.date = "";
                }

                // 设置其他搜索结果数据
                searchResultData.memo = jSONArray.getJSONObject(i).getString("Memo");           // 备注信息
                searchResultData.color = jSONArray.getJSONObject(i).getInt("Color");           // 显示颜色
                searchResultData.compamy = jSONArray.getJSONObject(i).getString("CompanyInfo"); // 公司信息
                searchResultData.action = jSONArray.getJSONObject(i).getInt("ActionType");     // 操作类型

                // 添加到结果列表
                arrayList.add(searchResultData);
            } catch (Exception e) {
                // 处理单个项目时出错，记录错误但继续处理其他项目
                e.printStackTrace();
            }
        }

        // 更新结果数量显示
        this.searchResultCount.setText("총 " + length + "건");  // 总共X条

        if (length > 0) {
            // 有搜索结果时显示列表
            this.searchResultLV.setVisibility(0);      // 显示结果列表
            this.searchResultEmptyTV.setVisibility(8); // 隐藏空结果提示
            // 创建并设置列表适配器
            this.searchResultListAdapter = new SearchResultListAdapter(this.baseActivity, 0, arrayList);
            this.searchResultLV.setAdapter((ListAdapter) this.searchResultListAdapter);
        } else {
            // 无搜索结果时显示空结果提示
            this.searchResultLV.setVisibility(8);      // 隐藏结果列表
            this.searchResultEmptyTV.setVisibility(0); // 显示空结果提示
        }
    }

    /**
     * 处理各种操作按钮的点击事件
     * 根据点击的按钮执行相应的操作（拨号、短信、社交媒体搜索等）
     * @param view 被点击的视图
     */
    @Override
    public void onClick(View view) {
        // 获取格式化的电话号码（带连字符）
        String hypenPhoneNumber = Utils.getHypenPhoneNumber(Global.Incoming_Call_Number);

        switch (view.getId()) {
            case R.id.facebookBT:
                // Facebook搜索：在WebView中打开Facebook搜索页面
                this.baseActivity.gotoWebSearchFragment("https://m.facebook.com/search?q=" + hypenPhoneNumber);
                break;

            case R.id.googleBT:
                // Google搜索：在WebView中打开Google搜索页面
                this.baseActivity.gotoWebSearchFragment("https://www.google.com/search?q=" + hypenPhoneNumber);
                break;

            case R.id.lineBT:
                try {
                    // 启动Line应用
                    Intent launchIntentForPackage = getContext().getPackageManager().getLaunchIntentForPackage("jp.naver.line.android");
                    launchIntentForPackage.addFlags(67108864);  // FLAG_ACTIVITY_NEW_TASK
                    startActivity(launchIntentForPackage);
                    break;
                } catch (Exception unused) {
                    // Line应用未安装时显示提示
                    Toast.makeText(getContext(), "나인앱이 없습니다.", 0).show();  // Line应用不存在
                    return;
                }

            case R.id.makeCallBT:
                // 拨打电话：创建拨号Intent
                Intent intent = new Intent("android.intent.action.CALL");
                intent.setData(Uri.parse("tel:" + hypenPhoneNumber));
                startActivity(intent);
                break;

            case R.id.sendSMSBT:
                // 发送短信：创建短信Intent
                Intent intent2 = new Intent("android.intent.action.VIEW");
                intent2.setType("vnd.android-dir/mms-sms");
                intent2.putExtra("address", hypenPhoneNumber);  // 收件人号码
                intent2.putExtra("sms_body", "");              // 短信内容（空）
                try {
                    startActivity(intent2);
                    break;
                } catch (ActivityNotFoundException unused2) {
                    // 短信应用启动失败时显示错误提示
                    Toast.makeText(this.baseActivity, "SMS failed!", 0).show();
                    return;
                }
            case R.id.talkBT:
                try {
                    // 启动KakaoTalk应用
                    Intent launchIntentForPackage2 = getContext().getPackageManager().getLaunchIntentForPackage("com.kakao.talk");
                    launchIntentForPackage2.addFlags(67108864);  // FLAG_ACTIVITY_NEW_TASK
                    startActivity(launchIntentForPackage2);
                    break;
                } catch (Exception unused3) {
                    // KakaoTalk应用未安装时显示提示
                    Toast.makeText(getContext(), "카카오톡앱이 없습니다.", 0).show();  // KakaoTalk应用不存在
                    return;
                }
        }
    }

    /**
     * 从服务器获取搜索结果
     * 调用联系人工具类查询电话号码信息
     * @param str 要查询的电话号码
     */
    private void getSearchResultFromServer(String str) {
        // 调用查询方法，如果查询开始则显示进度对话框
        if (UtilContact.getInstance(getContext()).QueryPhoneNumber(
                str,  // 电话号码
                Boolean.valueOf(UtilSetting.getInstance(getContext()).TODAY_SHOW).booleanValue(),  // 是否显示今日信息
                Global.SearchHistory ? 2 : 0,  // 搜索类型：历史搜索为2，新搜索为0
                0,    // 其他参数
                this.QueryPhoneNumberResultHandler)) {  // 结果处理器
            // 显示加载进度对话框
            showProgress(getResources().getString(R.string.wait));
        }
    }
}