package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.Switch;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilSetting;

/**
 * 设置Fragment
 * 管理应用的各种设置选项，包括弹窗显示、位置等
 */
public class SettingFragment extends BaseFragment {
    /** 取消按钮 */
    private Button m_btnCancel;
    /** 确认按钮 */
    private Button m_btnConfirm;
    /** 底部位置单选按钮 */
    private RadioButton m_rbBottom;
    /** 中间位置单选按钮 */
    private RadioButton m_rbMiddle;
    /** 顶部位置单选按钮 */
    private RadioButton m_rbTop;
    /** 显示弹窗剩余开关 */
    private Switch m_swhShowPopupRemain;
    /** 显示访问开关 */
    private Switch m_swhShowVisit;
    /** 设置工具类实例 */
    private UtilSetting utilSetting;
    /** Fragment根视图 */
    private View view;

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局并初始化设置工具类
            this.view = layoutInflater.inflate(R.layout.fragment_setting, viewGroup, false);
            this.utilSetting = UtilSetting.getInstance(getContext());
            initUI(this.view);
        }
        return this.view;
    }

    /**
     * Fragment暂停时的回调
     * 保存用户的设置选项
     */
    @Override
    public void onPause() {
        super.onPause();
        // 保存开关状态
        this.utilSetting.TODAY_SHOW = this.m_swhShowVisit.isChecked();
        this.utilSetting.POPUP_REMAIN = this.m_swhShowPopupRemain.isChecked();

        // 保存弹窗位置设置
        if (this.m_rbTop.isChecked()) {
            this.utilSetting.POPUP_POSITION = 0;  // 顶部
        } else if (this.m_rbMiddle.isChecked()) {
            this.utilSetting.POPUP_POSITION = 1;  // 中间
        } else {
            this.utilSetting.POPUP_POSITION = 2;  // 底部
        }
        // 保存设置到本地
        this.utilSetting.saveSetting();
    }

    /**
     * 加载设置信息
     * 从设置工具类中读取保存的设置并更新UI控件状态
     */
    private void LoadSettingInfo() {
        // 设置显示访问开关的状态
        this.m_swhShowVisit.setChecked(this.utilSetting.TODAY_SHOW);
        // 设置显示弹窗剩余开关的状态
        this.m_swhShowPopupRemain.setChecked(this.utilSetting.POPUP_REMAIN);

        // 先清除所有位置单选按钮的选中状态
        this.m_rbTop.setChecked(false);
        this.m_rbMiddle.setChecked(false);
        this.m_rbBottom.setChecked(false);

        // 根据保存的弹窗位置设置相应的单选按钮
        if (this.utilSetting.POPUP_POSITION == 0) {
            // 位置0：顶部
            this.m_rbTop.setChecked(true);
        } else if (this.utilSetting.POPUP_POSITION == 1) {
            // 位置1：中间
            this.m_rbMiddle.setChecked(true);
        } else {
            // 位置2或其他：底部
            this.m_rbBottom.setChecked(true);
        }
    }

    /**
     * 初始化用户界面
     * 设置各种UI控件并配置单选按钮的互斥逻辑
     * @param view Fragment的根视图
     */
    private void initUI(View view) {
        // 初始化开关控件
        this.m_swhShowVisit = (Switch) view.findViewById(R.id.swhShowTodayCall);        // 显示今日通话开关
        this.m_swhShowPopupRemain = (Switch) view.findViewById(R.id.swhShowPopupRemain); // 显示弹窗剩余开关

        // 初始化位置单选按钮
        this.m_rbTop = (RadioButton) view.findViewById(R.id.chkTop);        // 顶部位置
        this.m_rbMiddle = (RadioButton) view.findViewById(R.id.chkMiddle);   // 中间位置
        this.m_rbBottom = (RadioButton) view.findViewById(R.id.chkBottom);   // 底部位置

        // 设置顶部位置单选按钮的点击监听器
        this.m_rbTop.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                // 选择顶部时，取消其他位置的选择
                SettingFragment.this.m_rbMiddle.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });

        // 设置中间位置单选按钮的点击监听器
        this.m_rbMiddle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                // 选择中间时，取消其他位置的选择
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });

        // 设置底部位置单选按钮的点击监听器
        this.m_rbBottom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view2) {
                // 选择底部时，取消其他位置的选择
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbMiddle.setChecked(false);
            }
        });

        // 加载并应用保存的设置信息
        LoadSettingInfo();
    }
}