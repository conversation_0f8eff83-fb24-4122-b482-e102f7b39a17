package com.developer.faker.Fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.RadioButton;
import android.widget.Switch;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilSetting;

/**
 * 设置Fragment
 * 管理应用的各种设置选项，包括弹窗显示、位置等
 */
public class SettingFragment extends BaseFragment {
    /** 取消按钮 */
    private Button m_btnCancel;
    /** 确认按钮 */
    private Button m_btnConfirm;
    /** 底部位置单选按钮 */
    private RadioButton m_rbBottom;
    /** 中间位置单选按钮 */
    private RadioButton m_rbMiddle;
    /** 顶部位置单选按钮 */
    private RadioButton m_rbTop;
    /** 显示弹窗剩余开关 */
    private Switch m_swhShowPopupRemain;
    /** 显示访问开关 */
    private Switch m_swhShowVisit;
    /** 设置工具类实例 */
    private UtilSetting utilSetting;
    /** Fragment根视图 */
    private View view;

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局并初始化设置工具类
            this.view = layoutInflater.inflate(R.layout.fragment_setting, viewGroup, false);
            this.utilSetting = UtilSetting.getInstance(getContext());
            initUI(this.view);
        }
        return this.view;
    }

    /**
     * Fragment暂停时的回调
     * 保存用户的设置选项
     */
    @Override
    public void onPause() {
        super.onPause();
        // 保存开关状态
        this.utilSetting.TODAY_SHOW = this.m_swhShowVisit.isChecked();
        this.utilSetting.POPUP_REMAIN = this.m_swhShowPopupRemain.isChecked();

        // 保存弹窗位置设置
        if (this.m_rbTop.isChecked()) {
            this.utilSetting.POPUP_POSITION = 0;  // 顶部
        } else if (this.m_rbMiddle.isChecked()) {
            this.utilSetting.POPUP_POSITION = 1;  // 中间
        } else {
            this.utilSetting.POPUP_POSITION = 2;  // 底部
        }
        // 保存设置到本地
        this.utilSetting.saveSetting();
    }

    private void LoadSettingInfo() {
        this.m_swhShowVisit.setChecked(this.utilSetting.TODAY_SHOW);
        this.m_swhShowPopupRemain.setChecked(this.utilSetting.POPUP_REMAIN);
        this.m_rbTop.setChecked(false);
        this.m_rbMiddle.setChecked(false);
        this.m_rbBottom.setChecked(false);
        if (this.utilSetting.POPUP_POSITION == 0) {
            this.m_rbTop.setChecked(true);
        } else if (this.utilSetting.POPUP_POSITION == 1) {
            this.m_rbMiddle.setChecked(true);
        } else {
            this.m_rbBottom.setChecked(true);
        }
    }

    private void initUI(View view) {
        this.m_swhShowVisit = (Switch) view.findViewById(R.id.swhShowTodayCall);
        this.m_swhShowPopupRemain = (Switch) view.findViewById(R.id.swhShowPopupRemain);
        this.m_rbTop = (RadioButton) view.findViewById(R.id.chkTop);
        this.m_rbMiddle = (RadioButton) view.findViewById(R.id.chkMiddle);
        this.m_rbBottom = (RadioButton) view.findViewById(R.id.chkBottom);
        this.m_rbTop.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.1
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbMiddle.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });
        this.m_rbMiddle.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.2
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbBottom.setChecked(false);
            }
        });
        this.m_rbBottom.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Fragment.SettingFragment.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view2) {
                SettingFragment.this.m_rbTop.setChecked(false);
                SettingFragment.this.m_rbMiddle.setChecked(false);
            }
        });
        LoadSettingInfo();
    }
}