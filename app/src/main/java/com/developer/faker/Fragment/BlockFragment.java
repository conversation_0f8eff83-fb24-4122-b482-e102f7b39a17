package com.developer.faker.Fragment;

import android.os.Bundle;
import android.support.design.widget.TabLayout;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentTransaction;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.developer.faker.R;
import com.developer.faker.Utils.UtilBlock;

/**
 * 拦截功能主Fragment
 * 管理拦截功能的三个子页面：设置、号码管理、历史记录
 */
public class BlockFragment extends BaseFragment {
    /** Fragment根视图 */
    View view;
    /** 拦截设置子Fragment */
    private BlockFragmentSetting blockFSetting = null;
    /** 拦截号码管理子Fragment */
    private BlockFragmentNumbers blockFNumbers = null;
    /** 拦截历史记录子Fragment */
    private BlockFragmentHistory blockFHistory = null;

    /**
     * 创建Fragment视图
     * @param layoutInflater 布局填充器
     * @param viewGroup 父视图组
     * @param bundle 保存的状态
     * @return Fragment的根视图
     */
    @Override
    public View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        if (this.view == null) {
            // 填充布局并初始化UI
            this.view = layoutInflater.inflate(R.layout.fragment_block, viewGroup, false);
            initUI();
        }
        return this.view;
    }

    /**
     * Fragment创建时的回调
     * 加载拦截设置并初始化子Fragment
     * @param bundle 保存的实例状态
     */
    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        // 加载拦截设置
        UtilBlock.getInstance(getContext()).LoadSetting();
        // 初始化子Fragment
        this.blockFSetting = new BlockFragmentSetting();
        this.blockFNumbers = new BlockFragmentNumbers();
        this.blockFHistory = new BlockFragmentHistory();
    }

    /**
     * Fragment暂停时的回调
     * 保存拦截设置
     */
    @Override
    public void onPause() {
        super.onPause();
        // 保存拦截设置
        UtilBlock.getInstance(getContext()).SaveSetting();
    }

    /**
     * Fragment停止时的回调
     */
    @Override
    public void onStop() {
        super.onStop();
    }

    /**
     * 初始化用户界面
     * 设置Tab选项卡的监听器并默认显示设置页面
     */
    private void initUI() {
        // 设置Tab选项卡的选择监听器
        ((TabLayout) this.view.findViewById(R.id.tabBlockCtrl)).addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabReselected(TabLayout.Tab tab) {
                // Tab重新选择时的处理（暂无操作）
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                // Tab取消选择时的处理（暂无操作）
            }

            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                // Tab选择时的处理，根据位置切换到对应的Fragment
                int position = tab.getPosition();
                if (position == 0) {
                    // 第一个Tab：拦截设置
                    BlockFragment.this.gotoFrag_BlockSetting();
                } else if (position == 1) {
                    // 第二个Tab：拦截号码管理
                    BlockFragment.this.gotoFrag_BlockNumbers();
                } else {
                    if (position != 2) {
                        return;
                    }
                    // 第三个Tab：拦截历史记录
                    BlockFragment.this.gotoFrag_BlockHistory();
                }
            }
        });
        // 默认显示拦截设置页面
        gotoFrag_BlockSetting();
    }

    /**
     * 跳转到拦截设置Fragment
     */
    public void gotoFrag_BlockSetting() {
        gotoFragment(this.blockFSetting);
    }

    /**
     * 跳转到拦截号码管理Fragment
     */
    public void gotoFrag_BlockNumbers() {
        gotoFragment(this.blockFNumbers);
    }

    /**
     * 跳转到拦截历史记录Fragment
     */
    public void gotoFrag_BlockHistory() {
        gotoFragment(this.blockFHistory);
    }

    /**
     * 通用的Fragment跳转方法
     * @param fragment 要显示的Fragment
     */
    private void gotoFragment(Fragment fragment) {
        // 开始Fragment事务
        FragmentTransaction fragmentTransactionBeginTransaction = getFragmentManager().beginTransaction();
        // 替换当前Fragment
        fragmentTransactionBeginTransaction.replace(R.id.block_content, fragment);
        // 提交事务
        fragmentTransactionBeginTransaction.commit();
    }
}