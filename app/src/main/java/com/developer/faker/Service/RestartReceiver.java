package com.developer.faker.Service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

/**
 * 重启接收器
 * 用于接收系统广播并重新启动主服务，确保服务的持续运行
 */
public class RestartReceiver extends BroadcastReceiver {
    // 重启服务的动作标识
    public static String ACTION_RESTART_ALARM_SERVICE = "ACTION_RESTART_ALARM_SERVICE";

    /**
     * 接收广播的回调方法
     * 当接收到重启服务的广播时，重新启动主服务
     * @param context 上下文
     * @param intent 接收到的Intent
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        // 检查是否是重启服务的广播
        if (intent.getAction().equals(ACTION_RESTART_ALARM_SERVICE)) {
            // 创建启动主服务的Intent
            Intent intent2 = new Intent(context, (Class<?>) MainService.class);

            // 根据Android版本选择启动方式
            if (Build.VERSION.SDK_INT >= 26) {
                // Android 8.0及以上版本使用前台服务
                context.startForegroundService(intent2);
            } else {
                // 低版本使用普通服务
                context.startService(intent2);
            }
        }
    }
}