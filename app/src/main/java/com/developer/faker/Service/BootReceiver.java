package com.developer.faker.Service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

/**
 * 开机启动接收器
 * 监听系统开机完成广播，自动启动主服务
 */
public class BootReceiver extends BroadcastReceiver {

    /**
     * 接收广播的回调方法
     * 当系统开机完成时自动启动主服务
     * @param context 上下文
     * @param intent 接收到的Intent
     */
    @Override
    public void onReceive(Context context, Intent intent) {
        // 检查是否是开机完成广播
        if ("android.intent.action.BOOT_COMPLETED".equals(intent.getAction())) {
            // 创建启动主服务的Intent
            Intent intent2 = new Intent(context, (Class<?>) MainService.class);

            // 根据Android版本选择启动方式
            if (Build.VERSION.SDK_INT >= 26) {
                // Android 8.0及以上版本使用前台服务
                context.startForegroundService(intent2);
            } else {
                // 低版本使用普通服务
                context.startService(intent2);
            }
        }
    }
}