package com.developer.faker.Service;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Icon;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import com.developer.faker.R;
import com.developer.faker.Data.RecentIncomeInfo;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.RC4;
import com.developer.faker.Utils.UtilAuth;
import com.developer.faker.Utils.UtilBlock;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.UtilSetting;
import com.developer.faker.Utils.Utils;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import org.json.JSONArray;
import org.json.JSONObject;
import android.widget.ImageView;
import android.content.Context;

/* loaded from: classes.dex */
public class FloatingViewService extends Service {
    private View mFloatingView;
    private WindowManager mWindowManager;
    LinearLayout m_LinearLayout;
    ScrollView m_ScrollView;
    Button m_btnDetail;
    TextView m_txtNewNotice;
    TextView m_txtPhoneNumber;
    TextView m_txtResultCount;
    TextView m_txtResultPhoneNumber;
    View m_viewDetail;
    BlinkTimer blinkTimer = new BlinkTimer();
    Timer timer = new Timer();
    public boolean check_flag = false;
    private Handler m_Handler = new Handler() { // from class: com.developer.faker.Service.FloatingViewService.2
        @Override // android.os.Handler
        public void handleMessage(Message message) {
            super.handleMessage(message);
            if (message.what == 10) {
                String str = (String) message.obj;
                if (str == null || str.isEmpty()) {
                    return;
                }
                FloatingViewService.this.m_txtPhoneNumber.setText(str);
                FloatingViewService.this.check_flag = true;
                return;
            }
            if (message.what == 0 || message.what == 1) {
                try {
                    FloatingViewService.this.onResultFromServer(message.what, message.obj);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                FloatingViewService.this.check_flag = false;
            }
        }
    };
    private String mCallNumber = "";
    final int[] response_type = {R.drawable.icon_none, R.drawable.icon_reject, R.drawable.icon_accept, R.drawable.icon_miss, R.drawable.icon_sms};
    final int[] category_type = {R.drawable.icon_0, R.drawable.icon_1, R.drawable.icon_2, R.drawable.icon_3, R.drawable.icon_4, R.drawable.icon_5, R.drawable.icon_6, R.drawable.icon_7, R.drawable.icon_8, R.drawable.icon_9, R.drawable.icon_10, R.drawable.icon_11, R.drawable.icon_12};

    @Override // android.app.Service
    public IBinder onBind(Intent intent) {
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void foreGroundStop() {
        NewPhonecallReceiver.isIncoming = false;
        if (Build.VERSION.SDK_INT >= 26) {
            stopForeground(true);
            stopForeground(1);
            stopSelf();
            return;
        }
        new Handler(Looper.getMainLooper()).post(new Runnable() { // from class: com.developer.faker.Service.FloatingViewService.1
            @Override // java.lang.Runnable
            public void run() {
                FloatingViewService.this.stopForeground(true);
                FloatingViewService.this.stopSelf();
                FloatingViewService.this.stopSelfResult(1);
                FloatingViewService floatingViewService = FloatingViewService.this;
                floatingViewService.stopService(new Intent(floatingViewService, (Class<?>) FloatingViewService.class));
                FloatingViewService.this.onDestroy();
            }
        });
    }

    void createUI() {
        this.mFloatingView = LayoutInflater.from(this).inflate(R.layout.pop_up_window, (ViewGroup) null);
        DisplayMetrics displayMetrics = getApplicationContext().getResources().getDisplayMetrics();
        int i = displayMetrics.widthPixels;
        int i2 = displayMetrics.heightPixels;
        final WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams(-1, -2, Build.VERSION.SDK_INT >= 26 ? 2038 : 2002, 2629768, -3);
        layoutParams.gravity = 49;
        layoutParams.x = 0;
        int i3 = UtilSetting.getInstance(getBaseContext()).POPUP_POSITION;
        if (i3 == 0) {
            layoutParams.y = 0;
        } else if (i3 == 1) {
            layoutParams.y = (i2 / 10) * 3;
        } else {
            layoutParams.y = (i2 / 10) * 6;
        }
        this.mWindowManager = (WindowManager) getSystemService("window");
        this.mWindowManager.addView(this.mFloatingView, layoutParams);
        this.m_txtPhoneNumber = (TextView) this.mFloatingView.findViewById(R.id.txtPhoneNumber);
        this.m_txtPhoneNumber.setText(this.mCallNumber);
        this.m_txtNewNotice = (TextView) this.mFloatingView.findViewById(R.id.txtNewNotice);
        this.timer.scheduleAtFixedRate(this.blinkTimer, 0L, 500L);
        this.m_txtNewNotice.setVisibility(Utils.getInstance().getNewNoticeState(getBaseContext()) ? 0 : 8);
        Utils.getInstance().GetContactFromPhoneNumber(getApplicationContext(), this.mCallNumber, this.m_Handler);
        this.m_ScrollView = (ScrollView) this.mFloatingView.findViewById(R.id.scrollView);
        this.m_ScrollView.setVisibility(8);
        this.m_LinearLayout = (LinearLayout) this.mFloatingView.findViewById(R.id.lstDetail);
        this.m_btnDetail = (Button) this.mFloatingView.findViewById(R.id.btnDetail);
        this.m_viewDetail = this.mFloatingView.findViewById(R.id.viewDetail);
        this.m_viewDetail.setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.3
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                FloatingViewService.this.m_ScrollView.setVisibility(0);
                FloatingViewService.this.m_btnDetail.setVisibility(8);
                FloatingViewService.this.m_viewDetail.setVisibility(8);
                FloatingViewService.this.m_ScrollView.setBackground(new BitmapDrawable(FloatingViewService.this.getBaseContext().getResources(), BitmapFactory.decodeResource(FloatingViewService.this.getResources(), R.drawable.search_back)));
                Display defaultDisplay = FloatingViewService.this.mWindowManager.getDefaultDisplay();
                WindowManager.LayoutParams layoutParams2 = (WindowManager.LayoutParams) FloatingViewService.this.mFloatingView.getLayoutParams();
                Point point = new Point();
                defaultDisplay.getSize(point);
                layoutParams2.width = -1;
                layoutParams2.height = (point.y / 3) * 2;
                layoutParams2.y = point.y / 6;
                FloatingViewService.this.mWindowManager.updateViewLayout(FloatingViewService.this.mFloatingView, layoutParams2);
            }
        });
        this.m_txtResultCount = (TextView) this.mFloatingView.findViewById(R.id.txtResultCount);
        this.m_txtResultPhoneNumber = (TextView) this.mFloatingView.findViewById(R.id.txtResultPhoneNumber);
        if (Build.VERSION.SDK_INT < 28) {
            this.mFloatingView.findViewById(R.id.control).setVisibility(8);
        } else {
            this.mFloatingView.findViewById(R.id.control).setVisibility(0);
        }
        this.mFloatingView.findViewById(R.id.btnClose).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.4
            @Override // android.view.View.OnClickListener
            public void onClick(View view) {
                FloatingViewService.this.foreGroundStop();
            }
        });
        this.mFloatingView.findViewById(R.id.btnAccept).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.5
            @Override // android.view.View.OnClickListener
            public void onClick(View view){
                try {
                    Utils.getInstance().acceptCall(FloatingViewService.this.getApplicationContext());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        this.mFloatingView.findViewById(R.id.btnReject).setOnClickListener(new View.OnClickListener() { // from class: com.developer.faker.Service.FloatingViewService.6
            @Override // android.view.View.OnClickListener
            public void onClick(View view){
                try {
                    Utils.getInstance().rejectCall(FloatingViewService.this.getApplicationContext());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        this.mFloatingView.findViewById(R.id.titlebar).setOnTouchListener(new View.OnTouchListener() { // from class: com.developer.faker.Service.FloatingViewService.7
            private float initialTouchX;
            private float initialTouchY;
            private int initialX;
            private int initialY;

            @Override // android.view.View.OnTouchListener
            public boolean onTouch(View view, MotionEvent motionEvent) {
                int action = motionEvent.getAction();
                if (action == 0) {
                    this.initialX = layoutParams.x;
                    this.initialY = layoutParams.y;
                    this.initialTouchX = motionEvent.getRawX();
                    this.initialTouchY = motionEvent.getRawY();
                    return true;
                }
                if (action == 1) {
                    motionEvent.getRawX();
                    float f = this.initialTouchX;
                    motionEvent.getRawY();
                    float f2 = this.initialTouchY;
                    return true;
                }
                if (action != 2) {
                    return false;
                }
                layoutParams.x = this.initialX + ((int) (motionEvent.getRawX() - this.initialTouchX));
                layoutParams.y = this.initialY + ((int) (motionEvent.getRawY() - this.initialTouchY));
                FloatingViewService.this.mWindowManager.updateViewLayout(FloatingViewService.this.mFloatingView, layoutParams);
                return true;
            }
        });
    }

    private class BlinkTimer extends TimerTask {
        private int nBlink;

        private BlinkTimer() {
            this.nBlink = 0;
        }

        @Override // java.util.TimerTask, java.lang.Runnable
        @SuppressLint({"ResourceAsColor"})
        public void run() {
            if (this.nBlink == 0) {
                FloatingViewService.this.m_txtNewNotice.setTextColor(Color.rgb(255, 0, 0));
                this.nBlink = 1;
            } else {
                FloatingViewService.this.m_txtNewNotice.setTextColor(Color.rgb(200, 200, 200));
                this.nBlink = 0;
            }
        }
    }

    @Override // android.app.Service
    public void onCreate() {
        if (Build.VERSION.SDK_INT >= 26) {
            O.createNotification(this);
            return;
        }
        Notification.Builder builder = new Notification.Builder(this);
        builder.setContentTitle("");
        startForeground(1, builder.getNotification());
    }

    @Override // android.app.Service
    public void onDestroy() {
        super.onDestroy();
        View view = this.mFloatingView;
        if (view != null) {
            this.mWindowManager.removeView(view);
        }
        this.timer.cancel();
    }

    @Override // android.app.Service
    public int onStartCommand(Intent intent, int i, int i2) {
        if (Build.VERSION.SDK_INT >= 26) {
            O.createNotification(this);
        } else {
            Notification.Builder builder = new Notification.Builder(this);
            builder.setContentTitle("");
            startForeground(1, builder.getNotification());
        }
        if (!intent.getAction().contains("ACTION_SHOW_NUMBER")) {
            if (!intent.getAction().contains("ACTION_FOREGROUND_STOP")) {
                return 2;
            }
            foreGroundStop();
            return 2;
        }
        if (intent == null) {
            return 2;
        }
        this.mCallNumber = intent.getStringExtra("tel");
        TextUtils.isEmpty(this.mCallNumber);
        new Handler(Looper.getMainLooper()).post(new Runnable() { // from class: com.developer.faker.Service.FloatingViewService.8
            @Override // java.lang.Runnable
            public void run() {
                FloatingViewService.this.createUI();
            }
        });
        return 2;
    }

    public static class O {
        private static Notification buildNotification(Service service, String str) {
            return new Notification.Builder(service, str).setSmallIcon(Icon.createWithBitmap(Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888))).build();
        }

        private static String createChannel(Service service) throws IOException {
            String packageName = service.getPackageName();
            try {
                packageName = (String) service.getPackageManager().getApplicationLabel(service.getPackageManager().getApplicationInfo(service.getPackageName(), 8192));
            } catch (PackageManager.NameNotFoundException e) {
                UtilLogFile.getInstance(service.getApplicationContext()).writeLog(e.toString());
            }
            NotificationManager notificationManager = (NotificationManager) service.getSystemService("notification");
            NotificationChannel notificationChannel = new NotificationChannel(packageName, packageName, 1);
            notificationChannel.setVibrationPattern(new long[]{0});
            notificationManager.createNotificationChannel(notificationChannel);
            return packageName;
        }

        public static void createNotification(Service service) {
            try {
                service.startForeground(1, buildNotification(service, createChannel(service)));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void onResultFromServer(int responseType, Object responseData) throws Exception {
        try {
            // 检查响应类型
            if (responseType == 1) {
                throw new Exception("网络错误");
            }

            Context applicationContext = getApplicationContext();
            JSONObject responseJson = new JSONObject(responseData.toString());

            // 解密响应数据
            JSONObject decryptedData = new JSONObject(new String(
                RC4.getInstance().decrypt(responseJson.get("data").toString(), Const.AUTH_KEY).getBytes("UTF-8")));

            // 检查是否有错误
            if (decryptedData.has("Error")) {
                this.m_btnDetail.setText(decryptedData.getString("Error"));
                this.m_btnDetail.setEnabled(false);
                return;
            }

            // 解析响应数据
            boolean isLimit = decryptedData.getBoolean("islimit");
            boolean isBlockAll = decryptedData.getBoolean("isblockall");
            String phoneNumber = decryptedData.getString("phoneNumber");
            UtilAuth.getInstance(getBaseContext()).SetRemainMinutes(decryptedData.getInt("RemainMinutes"));
            JSONArray dataArray = new JSONArray(decryptedData.getString("Data"));
            int dataLength = dataArray.length();
            // 处理全部阻止的情况
            if (isBlockAll) {
                Utils.getInstance().rejectCall(applicationContext);
                UtilBlock.getInstance(applicationContext).addBlockHistory(phoneNumber, 5, System.currentTimeMillis());
                if (!UtilBlock.getInstance(applicationContext).IsBlockAll) {
                    UtilBlock.getInstance(applicationContext).IsBlockAll = true;
                    UtilBlock.getInstance(applicationContext).SaveSetting();
                }
                return;
            }

            // 重置全部阻止状态
            if (UtilBlock.getInstance(applicationContext).IsBlockAll) {
                UtilBlock.getInstance(applicationContext).IsBlockAll = false;
                UtilBlock.getInstance(applicationContext).SaveSetting();
            }

            // 处理限制的情况
            if (isLimit) {
                Utils.getInstance().rejectCall(applicationContext);
                UtilBlock.getInstance(applicationContext).addBlockHistory(phoneNumber, 2, System.currentTimeMillis());
                return;
            }

            // 准备处理数据列表
            ArrayList<RecentIncomeInfo> recentInfoList = new ArrayList<>();
            UtilBlock utilBlock = UtilBlock.getInstance(applicationContext);
            boolean isBlockCallExp = utilBlock.IsBlockCallExp;
            int callExpCount = 0;
            // 处理数据列表
            for (int i = 0; i < dataLength; i++) {
                try {
                    JSONObject dataItem = dataArray.getJSONObject(i);

                    // 设置第一个项目的详情按钮文本
                    if (i == 0) {
                        try {
                            String memo = dataItem.getString("Memo");
                            String companyInfo = dataItem.getString("CompanyInfo");
                            this.m_btnDetail.setText(memo + "[" + companyInfo + "]");
                            this.m_btnDetail.setEnabled(true);
                        } catch (Exception e) {
                            UtilLogFile.getInstance(getApplicationContext()).writeLog(e.toString());
                        }
                    }

                    // 创建列表项视图
                    View itemView = ((LayoutInflater) applicationContext.getSystemService("layout_inflater"))
                            .inflate(R.layout.adapter_phone, (ViewGroup) null, true);

                    String memo = dataItem.getString("Memo");
                    String companyInfo = dataItem.getString("CompanyInfo");
                    int color = dataItem.getInt("Color");
                    int actionType = dataItem.getInt("ActionType");

                    // 检查呼叫爆炸阻止
                    if (isBlockCallExp) {
                        if (!memo.contains("ㅋㅍ") && memo.contains("콜폭")) {
                            callExpCount++;
                        }
                    }

                    // 处理日期时间
                    Calendar calendar = Calendar.getInstance();
                    try {
                        Date date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(dataItem.getString("UpdatedDate"));
                        calendar.setTime(date);
                        String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());
                        String timeStr = new SimpleDateFormat("HH:mm:ss").format(calendar.getTime());

                        ((TextView) itemView.findViewById(R.id.txtUpdateTime)).setText(timeStr + "  ");
                        ((TextView) itemView.findViewById(R.id.txtUpdateDate)).setText(dateStr);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    // 设置联系人信息
                    TextView contactTextView = (TextView) itemView.findViewById(R.id.txtContact);
                    contactTextView.setText(memo);
                    contactTextView.setTextColor(color);
                    ((TextView) itemView.findViewById(R.id.txtCompany)).setText(companyInfo);

                    // 设置动作图标
                    ImageView actionImageView = (ImageView) itemView.findViewById(R.id.imgAction);
                    if (actionType >= 256) {
                        try {
                            actionImageView.setImageResource(this.category_type[actionType - 256]);
                        } catch (Exception e) {
                            actionImageView.setImageResource(this.category_type[0]);
                            e.printStackTrace();
                        }
                    } else {
                        actionImageView.setImageResource(this.response_type[actionType]);
                    }

                    // 添加到最近信息列表
                    RecentIncomeInfo recentIncomeInfo = new RecentIncomeInfo();
                    recentIncomeInfo.contactName = memo;
                    recentIncomeInfo.callDate = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss").format(calendar.getTime());
                    recentInfoList.add(recentIncomeInfo);

                    // 添加到界面
                    this.m_LinearLayout.addView(itemView);

                } catch (Exception e) {
                    UtilLogFile.getInstance(getApplicationContext()).writeLog(e.toString());
                }
            }

            // 检查呼叫爆炸阻止
            if (isBlockCallExp && callExpCount >= utilBlock.callExplosionCount) {
                utilBlock.AddCallExpList(Utils.getCorrectPhoneNumber(this.mCallNumber));
                Utils.getInstance().rejectCall(applicationContext);
                UtilBlock.getInstance(applicationContext).addBlockHistory(phoneNumber, 6, System.currentTimeMillis());
                return;
            }

            // 显示剩余天数
            int remainDays = UtilAuth.getInstance(getBaseContext()).GetRemainMinutes() / 1440;
            if (remainDays > 3) {
                ((TextView) this.mFloatingView.findViewById(R.id.txtRemainDay))
                        .setText("제휴일(D-" + remainDays + ")");
            } else {
                TextView remainDayTextView = (TextView) this.mFloatingView.findViewById(R.id.txtRemainDay);
                remainDayTextView.setText("만료일: " + UtilAuth.getInstance(getBaseContext()).GetLicenseEndDate());
                remainDayTextView.setTextColor(Color.rgb(255, 0, 0));
            }

            // 如果数据少于3条，添加空白项目
            if (dataLength <= 3) {
                for (int i = dataLength; i < 5; i++) {
                    View emptyView = ((LayoutInflater) applicationContext.getSystemService("layout_inflater"))
                            .inflate(R.layout.adapter_phone, (ViewGroup) null, true);
                    ((TextView) emptyView.findViewById(R.id.txtUpdateTime)).setText("");
                    ((TextView) emptyView.findViewById(R.id.txtUpdateDate)).setText("");
                    ((TextView) emptyView.findViewById(R.id.txtContact)).setText("");
                    ((TextView) emptyView.findViewById(R.id.txtCompany)).setText("");
                    emptyView.findViewById(R.id.imgAction).setVisibility(8);
                    this.m_LinearLayout.addView(emptyView);
                }
            }

            // 处理结果显示
            if (dataLength <= 0) {
                this.m_btnDetail.setText("정보없음");
                this.m_btnDetail.setEnabled(false);
                return;
            }

            this.m_btnDetail.setVisibility(8);
            this.m_viewDetail.setVisibility(0);
            this.m_txtResultCount.setText("내역조회(" + dataLength + ")");
            this.m_txtResultPhoneNumber.setText(Utils.getHypenPhoneNumber(this.mCallNumber));

            Collections.sort(recentInfoList);
            if (!this.check_flag && !recentInfoList.isEmpty()) {
                this.m_txtPhoneNumber.setText(recentInfoList.get(0).contactName);
            }
        } catch (Exception e7) {
            UtilLogFile.getInstance(getApplicationContext()).writeLog(e7.toString());
            this.m_btnDetail.setText(R.string.service_fail);
            this.m_btnDetail.setEnabled(false);
            e7.printStackTrace();
        }
    }
}