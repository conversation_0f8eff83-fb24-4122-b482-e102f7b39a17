package com.developer.faker.Adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.DrawerData;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.util.ArrayList;

/**
 * 侧边栏适配器
 * 用于显示主界面侧边栏的菜单项，包含图标、名称和新消息提示
 */
public class DrawerAdapter extends ArrayAdapter<DrawerData> {
    BaseActivity activity;  // 活动引用

    /**
     * 构造函数
     * @param baseActivity 基础活动
     * @param i 布局资源ID
     * @param arrayList 侧边栏数据列表
     */
    public DrawerAdapter(BaseActivity baseActivity, int i, ArrayList<DrawerData> arrayList) {
        super(baseActivity, i, arrayList);
        this.activity = baseActivity;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        bindView(i, view);  // 绑定数据到视图
        return view;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_drawer, viewGroup, false);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     */
    private void bindView(int i, View view) {
        // 获取UI组件
        ImageView imageView = (ImageView) view.findViewById(R.id.iconIV);    // 图标
        TextView textView = (TextView) view.findViewById(R.id.nameTV);       // 名称
        TextView textView2 = (TextView) view.findViewById(R.id.txtNew);      // 新消息提示

        DrawerData item = getItem(i);  // 获取数据项

        // 设置图标和名称
        imageView.setImageResource(item.icon.intValue());
        textView.setText(item.name);

        // 如果是第3个项目（公告），检查是否有新公告并显示提示
        if (i == 2) {
            if (Utils.getInstance().getNewNoticeState(getContext())) {
                textView2.setVisibility(0);  // 显示新消息提示
            } else {
                textView2.setVisibility(4);  // 隐藏新消息提示
            }
        }
    }
}