package com.developer.faker.Adapter;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.v4.os.EnvironmentCompat;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.BlockNumberHistory;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 拦截号码历史记录适配器
 * 用于显示被拦截的电话号码历史记录列表，包含拦截类型和时间信息
 */
public class BlockNumberHistoryAdapter extends ArrayAdapter<BlockNumberHistory> {
    BaseActivity activity;  // 活动引用

    /**
     * 构造函数
     * @param context 上下文
     * @param i 布局资源ID
     * @param list 拦截历史数据列表
     */
    public BlockNumberHistoryAdapter(@NonNull Context context, int i, @NonNull List<BlockNumberHistory> list) {
        super(context, i, list);
        this.activity = (BaseActivity) context;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        bindView(i, view);  // 绑定数据到视图
        return view;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_blockhistory, viewGroup, false);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     */
    private void bindView(int i, View view) {
        String str;
        BlockNumberHistory item = getItem(i);  // 获取数据项

        // 获取UI组件
        TextView textView = (TextView) view.findViewById(R.id.txt_phone);        // 电话号码
        TextView textView2 = (TextView) view.findViewById(R.id.txtDate);         // 拦截日期
        TextView textView3 = (TextView) view.findViewById(R.id.txtBlockComment); // 拦截类型说明

        // 设置电话号码（添加连字符格式）
        textView.setText(Utils.getHypenPhoneNumber(item.number));
        // 设置拦截日期时间
        textView2.setText(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(item.dateTick)));

        // 根据拦截类型设置说明文字
        switch (item.type) {
            case 1:
                str = "모르는번호 차단";      // 未知号码拦截
                break;
            case 2:
                str = "오늘전화문의 차단";    // 今日电话咨询拦截
                break;
            case 3:
                str = "지정번호 차단";        // 指定号码拦截
                break;
            case 4:
                str = "시작번호 차단";        // 开头号码拦截
                break;
            case 5:
                str = "모든번호 차단";        // 所有号码拦截
                break;
            case 6:
                str = "콜폭 차단";           // 呼叫轰炸拦截
                break;
            default:
                str = EnvironmentCompat.MEDIA_UNKNOWN;  // 未知类型
                break;
        }

        // 设置拦截类型说明
        textView3.setText(str);
        // 设置标签
        view.setTag(item);
    }
}