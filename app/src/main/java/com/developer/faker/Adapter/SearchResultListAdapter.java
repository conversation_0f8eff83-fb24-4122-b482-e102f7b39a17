package com.developer.faker.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import com.developer.faker.Data.SearchResultData;
import com.developer.faker.R;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;

/**
 * 搜索结果列表适配器
 * 用于显示电话号码搜索结果，包含公司信息、联系方式、更新时间和操作图标
 */
public class SearchResultListAdapter extends ArrayAdapter<SearchResultData> {
    final int[] category_type;    // 分类类型图标数组
    Context mContext;             // 上下文
    final int[] response_type;    // 响应类型图标数组

    /**
     * 构造函数
     * @param context 上下文
     * @param i 布局资源ID
     * @param arrayList 搜索结果数据列表
     */
    public SearchResultListAdapter(Context context, int i, ArrayList<SearchResultData> arrayList) {
        super(context, i, arrayList);

        // 初始化响应类型图标数组（无、拒接、接听、未接、短信）
        this.response_type = new int[]{R.drawable.icon_none, R.drawable.icon_reject,
                R.drawable.icon_accept, R.drawable.icon_miss, R.drawable.icon_sms};

        // 初始化分类类型图标数组（0-12种不同类型）
        this.category_type = new int[]{R.drawable.icon_0, R.drawable.icon_1, R.drawable.icon_2,
                R.drawable.icon_3, R.drawable.icon_4, R.drawable.icon_5, R.drawable.icon_6,
                R.drawable.icon_7, R.drawable.icon_8, R.drawable.icon_9, R.drawable.icon_10,
                R.drawable.icon_11, R.drawable.icon_12};

        this.mContext = context;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        try {
            bindView(i, view);  // 绑定数据到视图
        } catch (Exception e) {
            e.printStackTrace();
        }
        return view;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return ((LayoutInflater) this.mContext.getSystemService("layout_inflater"))
                .inflate(R.layout.adapter_phone, (ViewGroup) null);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     * @throws ParseException 日期解析异常
     */
    private void bindView(int i, View view) throws ParseException {
        SearchResultData searchResultData;

        // 获取UI组件
        TextView textView = (TextView) view.findViewById(R.id.txtCompany);      // 公司名称
        TextView textView2 = (TextView) view.findViewById(R.id.txtContact);     // 联系信息
        TextView textView3 = (TextView) view.findViewById(R.id.txtUpdateDate);  // 更新日期
        TextView textView4 = (TextView) view.findViewById(R.id.txtUpdateTime);  // 更新时间
        ImageView imageView = (ImageView) view.findViewById(R.id.imgAction);    // 操作图标

        SearchResultData item = getItem(i);  // 获取数据项

        // 设置基本信息
        textView.setText(item.compamy);      // 设置公司名称
        textView2.setText(item.memo);        // 设置联系备注
        textView2.setTextColor(item.color);  // 设置文字颜色
        textView3.setText(item.date);        // 设置日期

        // 根据操作类型设置相应的图标
        try {
            if (item.action >= 256) {
                // 如果操作值大于等于256，使用分类类型图标
                imageView.setImageResource(this.category_type[item.action - 256]);
                searchResultData = item;
            } else {
                // 否则使用响应类型图标
                imageView.setImageResource(this.response_type[item.action]);
                searchResultData = item;
            }
        } catch (Exception e) {
            // 异常时使用默认图标
            imageView.setImageResource(this.category_type[0]);
            e.printStackTrace();
            searchResultData = item;
        }

        // 格式化并设置日期时间显示
        try {
            // 解析原始日期时间字符串
            Date date = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss").parse(searchResultData.date);

            // 设置格式化后的日期
            textView3.setText(new SimpleDateFormat("yyyy-MM-dd").format(date));

            // 设置格式化后的时间
            String str = new SimpleDateFormat("HH:mm:ss").format(date);
            StringBuilder sb = new StringBuilder();
            sb.append(str);
            sb.append("  ");  // 添加空格
            String timeText = sb.toString();
            textView4.setText(timeText);
        } catch (ParseException e2) {
            e2.printStackTrace();
        }
    }
}