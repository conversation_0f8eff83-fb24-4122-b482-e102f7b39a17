package com.developer.faker.Adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.NoticeInfo;
import com.developer.faker.R;
import java.util.ArrayList;

/**
 * 公告列表适配器
 * 用于显示系统公告信息列表，包含标题、日期和内容
 */
public class NoticeListAdapter extends ArrayAdapter<NoticeInfo> {
    BaseActivity activity;  // 活动引用

    /**
     * 构造函数
     * @param baseActivity 基础活动
     * @param i 布局资源ID
     * @param arrayList 公告信息数据列表
     */
    public NoticeListAdapter(BaseActivity baseActivity, int i, ArrayList<NoticeInfo> arrayList) {
        super(baseActivity, i, arrayList);
        this.activity = baseActivity;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        bindView(i, view);  // 绑定数据到视图
        return view;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_noticelist, viewGroup, false);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     */
    private void bindView(int i, View view) {
        NoticeInfo item = getItem(i);  // 获取公告数据项

        // 设置公告标题
        ((TextView) view.findViewById(R.id.txtNoticeTitle)).setText(item.subject);
        // 设置公告日期
        ((TextView) view.findViewById(R.id.txtNoticeDate)).setText(item.date);
        // 设置公告内容
        ((TextView) view.findViewById(R.id.txtNoticeContent)).setText(item.content);
    }
}