package com.developer.faker.Adapter;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.developer.faker.Activity.BaseActivity;
import com.developer.faker.Data.BlockNumberData;
import com.developer.faker.Model.BlockNumberDeleteListener;
import com.developer.faker.R;
import com.developer.faker.Utils.Utils;
import java.util.ArrayList;

/**
 * 拦截号码列表适配器
 * 用于显示拦截号码列表，支持删除操作和不同类型的视觉区分
 */
public class BlockNumberListAdapter extends ArrayAdapter<BlockNumberData> {
    BaseActivity activity;                      // 活动引用
    BlockNumberDeleteListener mlistener;       // 删除监听器

    /**
     * 构造函数
     * @param baseActivity 基础活动
     * @param i 布局资源ID
     * @param arrayList 拦截号码数据列表
     */
    public BlockNumberListAdapter(BaseActivity baseActivity, int i, ArrayList<BlockNumberData> arrayList) {
        super(baseActivity, i, arrayList);
        this.mlistener = null;
        this.activity = baseActivity;
    }

    /**
     * 获取列表项视图
     * @param i 位置
     * @param view 复用的视图
     * @param viewGroup 父视图组
     * @return 列表项视图
     */
    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        if (view == null) {
            view = newView(viewGroup);  // 创建新视图
        }
        bindView(i, view);  // 绑定数据到视图
        return view;
    }

    /**
     * 设置删除按钮点击监听器
     * @param blockNumberDeleteListener 删除监听器
     */
    public void setOnClickCloseListener(BlockNumberDeleteListener blockNumberDeleteListener) {
        this.mlistener = blockNumberDeleteListener;
    }

    /**
     * 创建新的列表项视图
     * @param viewGroup 父视图组
     * @return 新创建的视图
     */
    private View newView(ViewGroup viewGroup) {
        return this.activity.getLayoutInflater().inflate(R.layout.adapter_blocklist, viewGroup, false);
    }

    /**
     * 绑定数据到视图
     * @param i 数据位置
     * @param view 要绑定的视图
     */
    private void bindView(final int i, View view) {
        BlockNumberData item = getItem(i);  // 获取数据项

        // 设置电话号码（添加连字符格式）
        ((TextView) view.findViewById(R.id.txt_phone)).setText(Utils.getHypenPhoneNumber(item.phoneNumber));
        // 设置今日拦截次数
        ((TextView) view.findViewById(R.id.txt_blockcount)).setText(String.valueOf(item.nTodayCount));

        // 根据拦截类型设置背景颜色（优先级号码显示绿色，普通号码显示白色）
        ((LinearLayout) view.findViewById(R.id.contanerlayout)).setBackgroundResource(
                item.nBlockType == BlockNumberData.BlockType_Pref ?
                        R.color.greenlight_alpha_color : R.color.white_alpha_color);

        // 如果设置了删除监听器，添加删除按钮点击事件
        if (this.mlistener != null) {
            view.findViewById(R.id.btnDelete).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view2) {
                    final BlockNumberData item2 = BlockNumberListAdapter.this.getItem(i);

                    // 创建删除确认对话框
                    AlertDialog.Builder builder = new AlertDialog.Builder(view2.getContext());
                    builder.setTitle("삭제");  // 删除
                    builder.setMessage(item2.phoneNumber + " 를 삭제하시겠습니까?");  // 是否删除此号码？

                    // 确认删除按钮
                    builder.setPositiveButton("예", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialogInterface, int i2) {
                            // 调用删除监听器
                            BlockNumberListAdapter.this.mlistener.onResult(item2);
                        }
                    });

                    // 取消按钮
                    builder.setNegativeButton("아니", (DialogInterface.OnClickListener) null);  // 否
                    builder.show();
                }
            });
        }

        // 设置标签
        view.setTag(item);
    }
}