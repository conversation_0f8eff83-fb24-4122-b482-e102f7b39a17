<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 应用主题 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- 自定义样式 -->
    <style name="courseDuration">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">#666666</item>
    </style>

    <style name="course_button_style">
        <item name="android:background">@drawable/course_button_style</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="normal_button_style">
        <item name="android:background">@drawable/normal_button_style</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="recall_button_style">
        <item name="android:background">@drawable/recall_button_style</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="mini_call_button">
        <item name="android:background">@drawable/mini_call_button</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="search_button">
        <item name="android:background">@drawable/search_button</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="social_button">
        <item name="android:background">@drawable/social_button</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">14sp</item>
    </style>

    <!-- 登录相关样式 -->
    <style name="login_edit">
        <item name="android:background">@drawable/login_edit</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <!-- 工具栏样式 -->
    <style name="ToolbarColoredBackArrow" parent="Widget.AppCompat.Toolbar">
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white</item>
    </style>

    <style name="toolbarWhiteText">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

</resources>
