<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.developer.faker">

    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_CONTACTS"/>
    <uses-permission android:name="android.permission.WRITE_CONTACTS"/>
    <uses-permission android:name="android.permission.RECEIVE_SMS"/>
    <uses-permission android:name="android.permission.READ_SMS"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE"/>
    <uses-permission android:name="android.permission.READ_CALL_LOG"/>
    <uses-permission android:name="android.permission.WRITE_CALL_LOG"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <application
        android:theme="@style/AppTheme"
        android:label="@string/app_name"
        android:icon="@mipmap/logo"
        android:allowBackup="true"
        android:supportsRtl="true"
        android:roundIcon="@mipmap/logo"
        android:appComponentFactory="android.support.v4.app.CoreComponentFactory">
        <activity
            android:theme="@style/AppTheme.NoActionBar"
            android:name="com.developer.faker.Activity.LoginActivity"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity
            android:theme="@style/AppTheme.NoActionBar"
            android:name="com.developer.faker.Activity.MainActivity"
            android:screenOrientation="portrait"/>
        <service
            android:name="com.developer.faker.Service.MainService"
            android:permission="android.permission.SYSTEM_ALERT_WINDOW"
            android:enabled="true">
            <intent-filter>
                <action android:name="com.developer.faker.Service.MainService"/>
            </intent-filter>
        </service>
        <receiver android:name="com.developer.faker.Service.RestartReceiver"/>
        <receiver android:name="com.developer.faker.Service.BootReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>
        <service
            android:name="com.developer.faker.Service.FloatingViewService"
            android:enabled="true"
            android:exported="false"/>
        <receiver
            android:name="com.developer.faker.Service.NewPhonecallReceiver"
            android:enabled="true">
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.PHONE_STATE"/>
                <action android:name="android.intent.action.NEW_OUTGOING_CALL"/>
                <action android:name="android.provider.Telephony.SMS_RECEIVED"/>
            </intent-filter>
        </receiver>
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:exported="false"
            android:authorities="com.developer.faker"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"/>
        </provider>
    </application>
</manifest>
