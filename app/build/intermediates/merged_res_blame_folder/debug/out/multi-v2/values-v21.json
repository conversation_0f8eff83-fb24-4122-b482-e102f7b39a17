{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeDebugResources/merged.dir/values-v21/values-v21.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/8fd5d53888178dd9013834ef7b031501/support-media-compat-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "265,268,272,276", "startColumns": "4,4,4,4", "startOffsets": "19504,19672,19961,20257", "endLines": "267,270,274,278", "endColumns": "12,12,12,12", "endOffsets": "19667,19830,20124,20419"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/6f81025bcc413a318d3343580ec76047/support-compat-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,354,470,596,722,850,1022", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,465,591,717,845,1017,1355"}, "to": {"startLines": "2,3,4,5,263,264,271,275,311,314", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,223,290,19262,19378,19835,20129,22137,22309", "endLines": "2,3,4,5,263,264,271,275,313,318", "endColumns": "103,63,66,63,115,125,125,127,12,12", "endOffsets": "154,218,285,349,19373,19499,19956,20252,22304,22642"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/5c77575677e88c861fe7832a575236ec/design-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,10,18,26", "startColumns": "4,4,4,4", "startOffsets": "55,484,910,1333", "endLines": "9,17,25,33", "endColumns": "10,10,10,10", "endOffsets": "479,905,1328,1763"}, "to": {"startLines": "279,287,295,303", "startColumns": "4,4,4,4", "startOffsets": "20424,20853,21279,21702", "endLines": "286,294,302,310", "endColumns": "10,10,10,10", "endOffsets": "20848,21274,21697,22132"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-2/files-2.1/c736e09b9b8b0f5b17e0f48c980de4cc/appcompat-v7-28.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,107,110,154,157,160,162,164,166,169,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,221,222,223,233,234,235,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4234,4383,4532,4644,4791,4944,5091,5166,5255,5342,5443,5546,8614,8799,11879,12076,12275,12398,12521,12634,12817,12948,13149,13238,13349,13582,13683,13778,13901,14030,14147,14324,14423,14558,14701,14836,14955,15156,15275,15368,15479,15535,15642,15837,15948,16081,16176,16267,16358,16475,16614,16685,16768,17448,17505,17563,18257", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,106,109,153,156,159,161,163,165,168,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,220,221,222,232,233,234,246,258", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4229,4378,4527,4639,4786,4939,5086,5161,5250,5337,5438,5541,8609,8794,11874,12071,12270,12393,12516,12629,12812,12943,13144,13233,13344,13577,13678,13773,13896,14025,14142,14319,14418,14553,14696,14831,14950,15151,15270,15363,15474,15530,15637,15832,15943,16076,16171,16262,16353,16470,16609,16680,16763,17443,17500,17558,18252,18958"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,111,114,158,161,164,166,168,170,173,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,225,226,227,237,238,239,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "354,445,548,651,756,863,972,1081,1190,1299,1408,1515,1618,1737,1892,2047,2152,2273,2374,2521,2662,2765,2884,2991,3094,3249,3420,3569,3734,3891,4042,4161,4533,4682,4831,4943,5090,5243,5390,5465,5554,5641,5742,5845,8913,9098,12178,12375,12574,12697,12820,12933,13116,13247,13448,13537,13648,13881,13982,14077,14200,14329,14446,14623,14722,14857,15000,15135,15254,15455,15574,15667,15778,15834,15941,16136,16247,16380,16475,16566,16657,16774,16913,16984,17067,17747,17804,17862,18556", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,110,113,157,160,163,165,167,169,172,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,224,225,226,236,237,238,250,262", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "440,543,646,751,858,967,1076,1185,1294,1403,1510,1613,1732,1887,2042,2147,2268,2369,2516,2657,2760,2879,2986,3089,3244,3415,3564,3729,3886,4037,4156,4528,4677,4826,4938,5085,5238,5385,5460,5549,5636,5737,5840,8908,9093,12173,12370,12569,12692,12815,12928,13111,13242,13443,13532,13643,13876,13977,14072,14195,14324,14441,14618,14717,14852,14995,15130,15249,15450,15569,15662,15773,15829,15936,16131,16242,16375,16470,16561,16652,16769,16908,16979,17062,17742,17799,17857,18551,19257"}}]}]}