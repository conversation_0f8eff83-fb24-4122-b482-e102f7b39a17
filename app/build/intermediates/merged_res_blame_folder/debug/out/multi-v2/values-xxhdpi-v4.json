{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeDebugResources/merged.dir/values-xxhdpi-v4/values-xxhdpi-v4.xml", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-xxhdpi/bools.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "41", "endOffsets": "92"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-xxhdpi/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,94,130,166,205,242,281,318,355,392,431,468,507,544,581,618,657,694,729,768,805,842,879,918,955,994,1033,1072,1109,1144,1181,1218,1255,1294,1329,1366,1401,1438,1475,1510,1547,1584,1619,1656,1691,1728,1763,1800", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "89,125,161,200,237,276,313,350,387,426,463,502,539,576,613,652,689,724,763,800,837,874,913,950,989,1028,1067,1104,1139,1176,1213,1250,1289,1324,1361,1396,1433,1470,1505,1542,1579,1614,1651,1686,1723,1758,1795,1830"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "97,136,172,208,247,284,323,360,397,434,473,510,549,586,623,660,699,736,771,810,847,884,921,960,997,1036,1075,1114,1151,1186,1223,1260,1297,1336,1371,1408,1443,1480,1517,1552,1589,1626,1661,1698,1733,1770,1805,1842", "endColumns": "38,35,35,38,36,38,36,36,36,38,36,38,36,36,36,38,36,34,38,36,36,36,38,36,38,38,38,36,34,36,36,36,38,34,36,34,36,36,34,36,36,34,36,34,36,34,36,34", "endOffsets": "131,167,203,242,279,318,355,392,429,468,505,544,581,618,655,694,731,766,805,842,879,916,955,992,1031,1070,1109,1146,1181,1218,1255,1292,1331,1366,1403,1438,1475,1512,1547,1584,1621,1656,1693,1728,1765,1800,1837,1872"}}]}]}