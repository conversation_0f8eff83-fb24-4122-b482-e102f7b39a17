{"logs": [{"outputFile": "/Users/<USER>/paker/app/build/intermediates/incremental/mergeDebugResources/merged.dir/values-sw600dp-v13/values-sw600dp-v13.xml", "map": [{"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "239"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "1233", "endLines": "22", "endColumns": "12", "endOffsets": "1417"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/integers.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "62", "endOffsets": "113"}, "to": {"startLines": "18", "startColumns": "4", "startOffsets": "1170", "endColumns": "62", "endOffsets": "1228"}}, {"source": "/Users/<USER>/paker/app/src/main/res/values-sw600dp/dimens.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,193,263,337,413,472,543,611,671,741,812,884,942,1000,1106", "endColumns": "68,68,69,73,75,58,70,67,59,69,70,71,57,57,105,63", "endOffsets": "119,188,258,332,408,467,538,606,666,736,807,879,937,995,1101,1165"}}]}]}