[{"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_shape_back_lightblue_stroke.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/shape_back_lightblue_stroke.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxhdpi_logo.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxhdpi/logo.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_login_back.PNG.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/login_back.PNG"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_login_mark_pwd.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/login_mark_pwd.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_2.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_2.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_android_question.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/android_question.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_activity_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/activity_main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha50.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha50.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_block_setting.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_block_setting.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_result.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_result.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_9.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_9.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_11.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_11.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_websearch.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_websearch.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_facebook.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_facebook.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_course_button_style.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/course_button_style.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_shape_back_white_stroke.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/shape_back_white_stroke.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_sms.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_sms.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-hdpi_ic_launcher.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-hdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_login_mark_email.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/login_mark_email.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_pop_up_notice.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/pop_up_notice.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_3.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_3.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xhdpi_logo.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xhdpi/logo.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/xml_provider_paths.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/xml/provider_paths.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_activity_login.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/activity_login.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_accept.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_accept.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_10.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_10.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_line.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/line.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/color_switch_thumb_material_light.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/color/switch_thumb_material_light.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-hdpi_notify_panel_notification_icon_bg.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-hdpi/notify_panel_notification_icon_bg.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-hdpi_logo.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-hdpi/logo.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_blocklist.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_blocklist.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_normal_button_style.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/normal_button_style.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_search_back1.PNG.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/search_back1.PNG"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_search.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_search.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_close.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_close.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_block_history.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_block_history.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_block_all.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_block_all.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_settings.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/settings.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/anim_slideout.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/anim/slideout.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_4.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_4.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_content_main_search.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/content_main_search.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_recall_button_style.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/recall_button_style.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_search_backicon.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/search_backicon.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha25.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha25.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_telegram_icon.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/telegram_icon.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_line.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_line.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/anim_alpha.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/anim/alpha.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_none.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_none.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_side_nav_bar.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/side_nav_bar.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_blockspecnum.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_blockspecnum.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_1.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_1.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_mini_call_button.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/mini_call_button.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_pop_up_window.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/pop_up_window.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_blockcallexp.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_blockcallexp.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_block_numbers.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_block_numbers.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_select_dialog_multichoice_material.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/select_dialog_multichoice_material.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha1.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha1.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_blockhistory.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_blockhistory.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_mark.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_mark.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/anim_translate.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/anim/translate.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_round_button.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/round_button.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/color_switch_thumb_material_dark.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/color/switch_thumb_material_dark.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_app_bar_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/app_bar_main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_select_dialog_singlechoice_material.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/select_dialog_singlechoice_material.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_call.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_call.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha0.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha0.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_recentcalllist.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_recentcalllist.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_social_button.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/social_button.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_ic_launcher.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_icon2.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/icon2.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_search_back.PNG.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/search_back.PNG"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_navigation_empty_icon.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/navigation_empty_icon.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_main_title.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/main_title.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_signin_line.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/signin_line.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-mdpi_notify_panel_notification_icon_bg.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-mdpi/notify_panel_notification_icon_bg.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_drawer.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_drawer.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_content_search_tabbar.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/content_search_tabbar.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_miss.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_miss.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_content_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/content_main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_kakaotalk.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_kakaotalk.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_block.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_block.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_search_icon.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/search_icon.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_img_back2.PNG.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/img_back2.PNG"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_nav_header_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/nav_header_main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_reject.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_reject.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_search.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_search.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_6.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_6.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_arrow_right.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/arrow_right.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha100.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha100.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_nav_cnt_back.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/nav_cnt_back.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_logo.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/logo.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_close.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_close.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/anim_slidein.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/anim/slidein.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_tooltip_frame_light.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/tooltip_frame_light.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_default_board.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/default_board.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_noticelist.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_noticelist.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_email.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/email.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_logout.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_logout.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_7.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_7.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_blocktodaycall.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_blocktodaycall.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_0.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_0.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/anim_bounce.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/anim/bounce.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_setting.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_setting.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_blockprefnum.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_blockprefnum.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-hdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_block.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_block.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_img_alpha75.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/img_alpha75.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/menu_main.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/menu/main.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_select_dialog_item_material.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/select_dialog_item_material.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_android_call.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/android_call.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_fragment_notice.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/fragment_notice.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_reject.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_reject.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_search_button.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/search_button.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_sms.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_sms.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_popup_blockunknown.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/popup_blockunknown.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_mail.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_mail.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xxhdpi_icon_nodata.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xxhdpi/icon_nodata.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_call.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/call.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_back.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_back.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_accept.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_accept.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable-xhdpi_notify_panel_notification_icon_bg.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable-xhdpi/notify_panel_notification_icon_bg.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_login_edit.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/login_edit.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxxhdpi_logo.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxxhdpi/logo.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_8.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_8.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_search_mark.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_search_mark.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-mdpi_social_google.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-mdpi/social_google.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_circle.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/circle.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_adapter_phone.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/adapter_phone.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_background.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_background.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_5.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_5.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_pop_close_gray.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/pop_close_gray.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_tooltip_frame_dark.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/tooltip_frame_dark.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/layout_support_simple_spinner_dropdown_item.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/layout/support_simple_spinner_dropdown_item.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_default_editbox.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/default_editbox.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_12.xml.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_12.xml"}, {"merged": "/Users/<USER>/paker/app/build/intermediates/res/merged/debug/drawable_icon_phone1.png.flat", "source": "/Users/<USER>/paker/app/src/main/res/drawable/icon_phone1.png"}]